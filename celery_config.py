from celery import Celery
from celery.schedules import crontab
import tasks
import time

def make_celery(app):
    celery = Celery(
        app.import_name,
        backend=app.config['result_backend'],
        broker=app.config['broker_url']      
    )
    celery.conf.update(app.config)
    
    return celery

CELERY_BEAT_SCHEDULE = {
    'run-backtesting-every-day': {
        'task': 'tasks.run_backtesting',
        'schedule': crontab(hour=21, minute=0, day_of_week='1-5'), # Runs at 9:00 PM UTC (4:00 PM ET) on weekdays
    },
    'run-test-compare-every-day': {
        'task': 'tasks.compare_result',
        'schedule': crontab(hour=20, minute=30, day_of_week='*'),  # Runs at 8:30 PM UTC every day
    },
    'run-model-performance': {
        'task': 'tasks.model_performance',
        'schedule': crontab(hour=0, minute=0, day_of_week=6),  # Runs at 00:00 UTC every Saturday
    },
}
