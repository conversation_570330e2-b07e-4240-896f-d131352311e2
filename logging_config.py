import logging
import sys

def setup_logging(name=None):
    logging.basicConfig(
        level=logging.INFO,  # Set to DEBUG for detailed logs
        format='%(asctime)s :: %(name)s :: %(levelname)s :: %(message)s',
        handlers=[
            logging.FileHandler("app.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    return logging.getLogger(name)

logger = setup_logging()
