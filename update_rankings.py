from algorithm import run_algorithm
from backtest import update_screener_with_backtest, populate_advanced_screener
from supabase import create_client, Client
import os
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get Supabase credentials from environment variables
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")

def main():
    try:
        # Initialize Supabase client
        try:
            supabase_client: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
        except Exception as e:
            logging.error(f"Failed to initialize Supabase client: {e}")
            raise e  # Propagate exception
        
        # Step 1: Update screener tables with backtest metrics
        logger.info("Updating screener tables with backtest metrics...")
        update_screener_with_backtest(supabase_client)
        
        # Step 2: Populate advanced screener with ML predictions
        logger.info("Populating advanced screener with ML predictions...")
        populate_advanced_screener(supabase_client)
        
        # Step 3: Run ranking algorithm
        logger.info("Running ranking algorithm...")
        run_algorithm()
        
        logger.info("Ranking update completed successfully!")
        
    except Exception as e:
        logger.error(f"Error updating rankings: {str(e)}")
        raise

if __name__ == "__main__":
    main() 