# This file can be empty 

from backtest.main import run_complete_analysis
from backtest.storage import store_backtest_results, store_backtest_results
from ml.models import train_and_save_model
import pandas as pd
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from industry import get_sector_industry_story

logger = logging.getLogger(__name__)

# Export the functions that are imported in app.py
__all__ = [
    'run_complete_analysis',
    'update_screener_with_backtest',
    'populate_advanced_screener',
    'train_models_for_screeners'
]

def update_screener_with_backtest(supabase_client):
    """
    Update screener tables with aggregated backtest metrics and populate advanced_screener.
    """
    logger.info("Starting to update screener tables with backtest metrics.")
    try:
        # Update Naked Put Screener
        naked_put_results = (
            supabase_client.table("naked_put_backtest_results_current").select("*").execute()
        )
        naked_put_df = pd.DataFrame(naked_put_results.data)

        if not naked_put_df.empty:
            agg_naked_put = (
                naked_put_df.groupby("screener_id")
                .agg(
                    average_profit=("daily_profit", "mean"),
                    total_profit=("cumulative_profit", "max"),
                    win_rate=("daily_profit", lambda x: (x > 0).mean()),
                )
                .reset_index()
            )

            # Prepare batch data for update
            update_data_naked_put = [
                {
                    "id": int(row["screener_id"]),
                    "average_profit": round(row["average_profit"], 2),
                    "total_profit": round(row["total_profit"], 2),
                    "win_rate": round(row["win_rate"], 2),
                }
                for _, row in agg_naked_put.iterrows()
            ]

            # Update in batch
            response = (
                supabase_client.table("naked_put_screener")
                .upsert(update_data_naked_put)
                .execute()
            )
            if hasattr(response, "error") and response.error:
                logger.error(f"Failed to update Naked Put Screener: {response.error}")
            else:
                logger.info("Updated Naked Put Screener with metrics.")
    except Exception as e:
        logger.error(f"Error updating screener tables: {e}")
        raise

def calculate_missing_values(backtest_results):
    """
    Calculate probability_of_profit, implied_volatility, ranking_score, potential_return, and other missing values.
    """
    if not backtest_results:
        return None
    
    df = pd.DataFrame(backtest_results)
    
    probability_of_profit = (df['daily_profit'] > 0).mean() * 100  # % of days with profit
    implied_volatility = df['capital'].std() / df['capital'].mean() if df['capital'].mean() != 0 else 0  # Approx IV
    ranking_score = (probability_of_profit + (100 - implied_volatility)) / 2  # Simple ranking metric
    potential_return = df['cumulative_profit'].iloc[-1] / df['capital'].iloc[0] * 100 if df['capital'].iloc[0] else 0
    
    return {
        "probability_of_profit": probability_of_profit,
        "implied_volatility": implied_volatility,
        "ranking_score": ranking_score,
        "potential_return": potential_return
    }

def populate_advanced_screener(supabase_client):
    """
    Update existing entries and insert new entries in advanced_screener table with latest metrics
    """
    try:
        naked_put_response = supabase_client.table("naked_put_screener_current").select("id", "symbol").execute()
        covered_call_response = supabase_client.table("covered_call_screener_current").select("id", "symbol").execute()
        
        naked_put_symbols = {(record['id'], record['symbol']) for record in naked_put_response.data}
        covered_call_symbols = {(record['id'], record['symbol']) for record in covered_call_response.data}

        logger.info(f"Found {len(naked_put_symbols)} naked put symbols and {len(covered_call_symbols)} covered call symbols")
        
        for screener_id, symbol in naked_put_symbols | covered_call_symbols:
            strategy_type = "Naked Put" if (screener_id, symbol) in naked_put_symbols else "Covered Call"
            
            backtest_results = supabase_client.table("naked_put_backtest_results" if strategy_type == "Naked Put" else "covered_call_backtest_results").select("*").eq("screener_id", screener_id).execute()
            
            if not backtest_results.data:
                logger.warning(f"No backtest results found for {symbol} ({strategy_type})")
                continue
                
            latest_result = sorted(backtest_results.data, key=lambda x: x.get('date', ''), reverse=True)[0]
            calculated_values = calculate_missing_values(backtest_results.data)
            
            # Fetch predicted probability from forward test tables
            forward_test_table = "naked_put_forwardtest_results" if strategy_type == "Naked Put" else "covered_call_forwardtest_results"
            forward_test_result = supabase_client.table(forward_test_table).select("predicted_probability").eq("screener_id", screener_id).eq("symbol", symbol).execute()
            
            predicted_probability = forward_test_result.data[0]['predicted_probability'] if forward_test_result.data else None
            
            existing = supabase_client.table("advanced_screener").select("*").eq("screener_id", screener_id).eq("strategy_type", strategy_type).execute()
            industries = get_sector_industry_story(symbol)

            upsert_data = {
                "screener_id": screener_id,
                "symbol": symbol,
                "strategy_type": strategy_type,
                "expiration_date": latest_result.get('date'),
                "strike": latest_result.get('strike_price', None),
                "probability_of_profit": calculated_values["probability_of_profit"],
                "implied_volatility": calculated_values["implied_volatility"],
                "ranking_score": calculated_values["ranking_score"],
                "potential_return": calculated_values["potential_return"],
                "ml_opinion": predicted_probability,
                "created_at": pd.Timestamp.utcnow().isoformat(),
                "sector": 'N/A',
                "industry": industries['sector'],
            }
            
            if not existing.data:
                response = supabase_client.table("advanced_screener").insert(upsert_data).execute()
                logger.info(f"Inserted new record for {symbol} ({strategy_type}) in advanced_screener")
            else:
                response = supabase_client.table("advanced_screener").update(upsert_data).eq("screener_id", screener_id).eq("strategy_type", strategy_type).execute()
                logger.info(f"Updated existing record for {symbol} ({strategy_type}) in advanced_screener")
        
        return {"status": "success"}
    
    except Exception as e:
        logger.error(f"Error updating advanced_screener table: {e}")
        raise

def train_models_for_screeners(supabase_client, get_historical_prices_func):
    """
    Train ML models for all symbols in the screener tables using pagination.
    """
    try:
        # Function to fetch symbols from a given table in batches of 1000
        def fetch_symbols_in_batches(table_name):
            limit = 1000
            offset = 0
            all_symbols = []
            
            while True:
                response = supabase_client.table(table_name) \
                                          .select('symbol') \
                                          .range(offset, offset+limit-1) \
                                          .execute()

                data = response.data

                # Debug logging for pagination
                if data and len(data) > 0:
                    logger.info(f"Fetched {len(data)} records from {table_name} starting at offset {offset}")
                    # Show a small sample of fetched symbols for debugging
                    sample_symbols = [record['symbol'].upper() for record in data[:10]]
                    logger.debug(f"Sample symbols from this batch: {sample_symbols}")
                else:
                    logger.info(f"No more records fetched from {table_name} at offset {offset}")
                    break

                # Extend our list with the new batch of symbols
                batch_symbols = [record['symbol'].upper() for record in data]
                all_symbols.extend(batch_symbols)
                offset += limit

            return all_symbols

        # Fetch symbols from each table
        naked_put_symbols = set(fetch_symbols_in_batches('naked_put_screener_current'))
        covered_call_symbols = set(fetch_symbols_in_batches('covered_call_screener_current'))

        # Log the counts obtained from pagination
        logger.info(f"Found {len(naked_put_symbols)} unique symbols in naked_put_screener_current after pagination.")
        logger.info(f"Found {len(covered_call_symbols)} unique symbols in covered_call_screener_current after pagination.")

        # Combine symbols and log
        all_symbols = naked_put_symbols.union(covered_call_symbols)
        logger.info(f"Found {len(all_symbols)} unique symbols to train models for.")

        # **Test without pagination**:
        # Fetch all records from one of the tables without any .range() calls to confirm total record count
        no_pagination_response = supabase_client.table('naked_put_screener_current').select('symbol').execute()
        logger.info(f"Test (no pagination) naked_put_screener_current returned {len(no_pagination_response.data)} records.")

        # Proceed with the rest of your training logic (unchanged)
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_symbol = {
                executor.submit(train_and_save_model, symbol, get_historical_prices_func, supabase_client): symbol
                for symbol in all_symbols
            }

            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    success = future.result()
                    if success:
                        logger.info(f"Model training and saving successful for {symbol}.")
                    else:
                        logger.warning(f"Model training failed for {symbol}.")
                except Exception as e:
                    logger.error(f"Exception occurred while training model for {symbol}: {e}")

        logger.info("Completed training models for all screener symbols.")
        return {"status": "success", "symbols_processed": len(all_symbols)}
    except Exception as e:
        logger.error(f"Error during bulk model training: {e}")
        raise
