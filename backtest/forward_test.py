import pandas as pd
from ml import models
import logging
import numpy as np
from backtest.position import Position
import torch
from datetime import date, timedelta, datetime
from dotenv import load_dotenv
import os
from polygon import RESTClient

logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Retrieve the Polygon API key from environment variables
POLYGON_API_KEY = os.getenv("POLYGON_API_KEY")
if not POLYGON_API_KEY:
    logger.error("POLYGON_API_KEY not set in environment variables.")
    # Optionally, raise an exception to halt execution
    # raise EnvironmentError("POLYGON_API_KEY not set in environment variables.")

# Initialize Polygon REST client globally
try:
    polygon_client = RESTClient(POLYGON_API_KEY)
except Exception as e:
    logger.error(f"Failed to initialize Polygon REST client: {e}")
    polygon_client = None  # Handle as per your application's requirement

def calculate_cci(historical_data, period=20):
    typical_price = (historical_data['high'] + historical_data['low'] + historical_data['close']) / 3
    moving_avg = typical_price.rolling(window=period).mean()
    mean_deviation = typical_price.rolling(window=period).apply(lambda x: np.mean(np.abs(x - np.mean(x))), raw=True)

    historical_data['CCI'] = (typical_price - moving_avg) / (0.015 * mean_deviation)
    return historical_data


def calculate_stochastic(historical_data, period=14, smooth_k=3, smooth_d=3):
    lowest_low = historical_data['low'].rolling(window=period).min()
    highest_high = historical_data['high'].rolling(window=period).max()

    historical_data['%K'] = 100 * (historical_data['close'] - lowest_low) / (highest_high - lowest_low)
    historical_data['%D'] = historical_data['%K'].rolling(window=smooth_d).mean()  # %D is a smoothed %K
    return historical_data


def calculate_atr(historical_data, period=14):
    high_low = historical_data['high'] - historical_data['low']
    high_close = abs(historical_data['high'] - historical_data['close'].shift(1))
    low_close = abs(historical_data['low'] - historical_data['close'].shift(1))

    true_range = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
    historical_data['ATR'] = true_range.rolling(window=period).mean()
    return historical_data

def calculate_rsi(df, period=14):
    """Calculate Relative Strength Index (RSI)."""
    delta = df['close'].diff(1)  # Price change
    
    gain = np.where(delta > 0, delta, 0)  # Gains
    loss = np.where(delta < 0, -delta, 0)  # Losses

    avg_gain = pd.Series(gain).rolling(window=period, min_periods=1).mean()
    avg_loss = pd.Series(loss).rolling(window=period, min_periods=1).mean()

    rs = avg_gain / (avg_loss + 1e-10)  # Avoid division by zero
    rsi = 100 - (100 / (1 + rs))
    
    return rsi

def compute_technical_indicators(historical_data: pd.DataFrame) -> pd.DataFrame:
    """
    Computes required technical indicators for backtesting.
    """
    # MACD Calculation
    historical_data['EMA12'] = historical_data['close'].ewm(span=12, adjust=False).mean()
    historical_data['EMA26'] = historical_data['close'].ewm(span=26, adjust=False).mean()
    historical_data['MACD'] = historical_data['EMA12'] - historical_data['EMA26']
    historical_data['Signal_Line'] = historical_data['MACD'].ewm(span=9, adjust=False).mean()

    # Moving Averages
    historical_data['MA10'] = historical_data['close'].rolling(window=10).mean()
    historical_data['MA50'] = historical_data['close'].rolling(window=50).mean()

    # Exponential Moving Averages
    historical_data['EMA10'] = historical_data['close'].ewm(span=10, adjust=False).mean()
    historical_data['EMA50'] = historical_data['close'].ewm(span=50, adjust=False).mean()

    # RSI Calculation
    delta = historical_data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    historical_data['RSI'] = 100 - (100 / (1 + rs))

    # On-Balance Volume (OBV)
    obv = np.where(historical_data['close'] > historical_data['close'].shift(1), 
                   historical_data['volume'], 
                   np.where(historical_data['close'] < historical_data['close'].shift(1), 
                            -historical_data['volume'], 0))
    historical_data['OBV'] = obv.cumsum()

    # Compute additional missing indicators
    historical_data = calculate_atr(historical_data)
    historical_data = calculate_stochastic(historical_data)
    historical_data = calculate_cci(historical_data)

    # Drop helper columns
    historical_data.drop(columns=['EMA12', 'EMA26'], inplace=True)

    return historical_data

def run_naked_put_forwardtest(symbol, df, screener_id, exp_date, strike, premium, iv, otm_prob):
    try:
        # Attempt to load ML model for predictions
        try:
            models.load_model_for_symbol(symbol)
            ml_available = True
        except Exception as e:
            logger.warning(f"ML model loading failed for {symbol}: {str(e)}")
            ml_available = False

        # Get the last row values for technical indicators
        last_row = df.iloc[-1]
        last_ma10 = last_row['MA10']
        last_ma50 = last_row['MA50']
        last_rsi = last_row['RSI']

        # Prepare the features based on the last row values
        features = [last_ma10, last_ma50, last_rsi]
        ml_opinion = 'Neutral'  # Default ml opinion
        prediction_prob = 0.0  # Default prediction probability
        confidence_score = 0.0  # Default confidence score
        print(f"Features: {features}")
        # If ML model is available, make the prediction for the entire symbol
        try:
            if ml_available:
                prediction_result = models.make_prediction_with_features(symbol, features)
                print(prediction_result)
                if prediction_result is not None:
                    prediction, prediction_prob = prediction_result
                else:
                    prediction_prob = 0.0
        except Exception as e:
            logger.warning(f"Prediction failed for {symbol}: {str(e)}")
            prediction_prob = 0.0  # Set to 0 if prediction fails

        prediction_date = datetime.now().date()

        # Store forwardtest result
        forwardtest_results = {
            "symbol": symbol,
            "screener_id": screener_id,
            "predicted_probability": prediction_prob,
            "prediction_date": str(datetime.now().date()),
            "predicted_direction": "UP" if prediction_prob > 0.55 else "DOWN" if prediction_prob < 0.45 else "SIDEWAYS",
            "features_used": ['open', 'high', 'low', 'close', 'volume', 'year', 'sin_day_of_week', 'cos_day_of_week', 'sin_month', 'cos_month'],
            "model_version": 'LSTM',
            "model_confidence_score": prediction_prob,
            "ml_opinion": ml_opinion,
            "expiration_date": exp_date,
            "target_date": exp_date,
            "strike_price": strike,
            "premium": premium,
            "implied_volatility": iv,
            "probability_otm": otm_prob,
        }

        # Convert results into a DataFrame for easier inspection and analysis
        logger.info(f"Successfully run forwardtest for {symbol}.")
        
        return forwardtest_results

    except Exception as e:
        logger.error(f"Error in run_naked_put_forwardtest: {str(e)}")
        return pd.DataFrame()

def run_covered_call_forwardtest(symbol, df, screener_id, exp_date, strike, premium, iv, otm_prob):
    try:
        # Attempt to load ML model for predictions
        try:
            models.load_model_for_symbol(symbol)
            ml_available = True
        except Exception as e:
            logger.warning(f"ML model loading failed for {symbol}: {str(e)}")
            ml_available = False

        # Get the last row values for technical indicators
        last_row = df.iloc[-1]
        last_ma10 = last_row['MA10']
        last_ma50 = last_row['MA50']
        last_rsi = last_row['RSI']

        # Prepare the features based on the last row values
        features = [last_ma10, last_ma50, last_rsi]
        ml_opinion = 'Neutral'  # Default ml opinion
        prediction_prob = 0.0  # Default prediction probability
        confidence_score = 0.0  # Default confidence score

        # If ML model is available, make the prediction for the entire symbol
        try:
            if ml_available:
                prediction_result = models.make_prediction_with_features(symbol, features)
                print(prediction_result)
                if prediction_result is not None:
                    prediction, prediction_prob = prediction_result
                    if prediction_prob > 0 and ml_opinion < 1:
                        ml_opinion = 'Netural'
                    elif prediction_prob == 0:
                        ml_opinion = 'Bearish'
                    elif prediction_prob == 1:
                        ml_opinion = 'Bull'
                else:
                    prediction_prob = 0.0
        except Exception as e:
            logger.warning(f"Prediction failed for {symbol}: {str(e)}")
            prediction_prob = 0.0  # Set to 0 if prediction fails

        # Store forwardtest result
        forwardtest_results = {
            "symbol": symbol, 
            "screener_id": screener_id,
            "predicted_probability": prediction_prob,
            "prediction_date": str(datetime.now().date()),
            "predicted_direction": "UP" if prediction_prob > 0.55 else "DOWN" if prediction_prob < 0.45 else "SIDEWAYS",
            "features_used": ['open', 'high', 'low', 'close', 'volume', 'year', 'sin_day_of_week', 'cos_day_of_week', 'sin_month', 'cos_month'],
            "model_version": 'LSTM',
            "model_confidence_score": prediction_prob,
            "ml_opinion": ml_opinion,
            "target_date": exp_date,
            "expiration_date": exp_date,
            "strike_price": strike,
            "premium": premium,
            "implied_volatility": iv,
            "probability_otm": otm_prob,
        }

        # Convert results into a DataFrame for easier inspection and analysis
        logger.info(f"Successfully run forwardtest for {symbol}.")
        
        return forwardtest_results

    except Exception as e:
        logger.error(f"Error in run_covered_call_forwardtest: {str(e)}")
        return pd.DataFrame()

def run_forward_test(symbol, screener_id, option_type, exp_date, strike, premium, iv, otm_prob):
    lookback_days = 180
    start_date = pd.Timestamp(date.today() - timedelta(days=lookback_days))
    end_date = datetime.now().strftime('%Y-%m-%d')
    logger.info(f"Fetching historical prices for {symbol} from {start_date} to {end_date}.")

    try:
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365 * 5)).strftime('%Y-%m-%d')

        response = polygon_client.get_aggs(
            ticker=symbol,
            multiplier=1,
            timespan='day',
            from_=start_date,
            to=end_date,
            limit=5000,
            adjusted=True
        )

        logger.debug(f'Raw response from Polygon API: {response[:3]}')

        if not response:
            logger.warning(f"No historical data found for symbol {symbol}.")
            return pd.DataFrame()

        data = [{
            'date': datetime.utcfromtimestamp(item.timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S'),
            'open': item.open,
            'high': item.high,
            'low': item.low,
            'close': item.close,
            'volume': item.volume
        } for item in response]

        df = pd.DataFrame(data)
        df['date'] = pd.to_datetime(df['date'])

        df['day_of_week'] = df['date'].dt.dayofweek
        df['month'] = df['date'].dt.month
        df['year'] = df['date'].dt.year

        df['sin_day_of_week'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['cos_day_of_week'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        df['sin_month'] = np.sin(2 * np.pi * df['month'] / 12)
        df['cos_month'] = np.cos(2 * np.pi * df['month'] / 12)

        df = df.drop(columns=['day_of_week', 'month'])

        # Calculate Moving Averages
        df['MA10'] = df['close'].rolling(window=10, min_periods=1).mean()
        df['MA50'] = df['close'].rolling(window=50, min_periods=1).mean()

        # Calculate RSI
        df['RSI'] = calculate_rsi(df, period=14)

        df = df.set_index('date', inplace=False)

        logger.info(f"DataFrame shape for {symbol}: {df.shape}")
        logger.info(f"First few rows of the DataFrame:\n{df.head()}")

        logger.info(f"Successfully fetched historical prices for {symbol}.")
        print(df)
        return run_naked_put_forwardtest(symbol, df, screener_id, exp_date, strike, premium, iv, otm_prob), run_covered_call_forwardtest(symbol, df, screener_id, exp_date, strike, premium, iv, otm_prob)

    except Exception as e:
        logger.error(f"Error in run_historical_forwardtest: {e}")
        return pd.DataFrame()
