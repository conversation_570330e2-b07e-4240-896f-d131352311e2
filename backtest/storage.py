def store_backtest_results(supabase_client, option_type, naked_put_historical_results, covered_call_historical_results, forward_results):
    """Store backtest results in database"""
    try:
        if option_type == 'naked_put':
            supabase_client.table("naked_put_backtest_results").upsert(naked_put_historical_results).execute()
    
        elif option_type == 'covered_call':
            supabase_client.table("covered_call_backtest_results").upsert(covered_call_historical_results).execute()
    
    except Exception as e:
        print(f"Failed to store {option_type} results: {e}")

    return


def store_forward_results(supabase_client, naked_put_future_results, covered_call_future_results, option_type):
    """Store forward results in database"""
    try:
        if option_type == 'naked_put':
            supabase_client.table("naked_put_forwardtest_results").upsert(naked_put_future_results).execute()
    
        elif option_type == 'covered_call':
            supabase_client.table("covered_call_forwardtest_results").upsert(covered_call_future_results).execute()
    
    except Exception as e:
        print(f"Failed to store {option_type} results: {e}")

    return