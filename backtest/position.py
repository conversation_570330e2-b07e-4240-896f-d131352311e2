from dataclasses import dataclass
from datetime import date
from decimal import Decimal

@dataclass
class Position:
    symbol: str
    strike: Decimal
    premium: Decimal
    expiration: date
    position_type: str  # 'naked_put' or 'covered_call'
    
    def __post_init__(self):
        self.is_open = False
        self.entry_date = None
        self.unrealized_pnl = Decimal('0')
        self.realized_pnl = Decimal('0')
        
    def open_position(self, entry_date: date):
        if not self.is_open:
            self.is_open = True
            self.entry_date = entry_date
            self.realized_pnl = self.premium * 100  # Collect premium once
            
    def calculate_daily_pnl(self, current_price: Decimal) -> tuple[Decimal, Decimal]:
        """Returns (daily_pnl, total_pnl)"""
        if not self.is_open:
            return Decimal('0'), Decimal('0')
            
        if self.position_type == 'naked_put':
            intrinsic = max(self.strike - current_price, Decimal('0'))
            new_unrealized = (self.premium * 100) - (intrinsic * 100)
        else:  # covered_call
            intrinsic = max(current_price - self.strike, Decimal('0'))
            new_unrealized = (self.premium * 100) - (intrinsic * 100)
            
        daily_pnl = new_unrealized - self.unrealized_pnl
        self.unrealized_pnl = new_unrealized
        
        return daily_pnl, self.realized_pnl + self.unrealized_pnl 