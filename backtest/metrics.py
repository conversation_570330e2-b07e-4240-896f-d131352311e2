import pandas as pd
from decimal import Decimal

def calculate_backtest_metrics(results_df: pd.DataFrame) -> dict:
    """Calculate key performance metrics from backtest results"""
    return {
        'total_pnl': results_df['total_pnl'].iloc[-1],
        'avg_daily_pnl': results_df['daily_pnl'].mean(),
        'win_rate': (results_df['daily_pnl'] > 0).mean(),
        'max_drawdown': calculate_max_drawdown(results_df['total_pnl']),
        'sharpe_ratio': calculate_sharpe_ratio(results_df['daily_pnl'])
    }

def calculate_max_drawdown(pnl_series):
    rolling_max = pnl_series.expanding().max()
    drawdowns = pnl_series - rolling_max
    return abs(drawdowns.min())

def calculate_sharpe_ratio(daily_returns, risk_free_rate=0.02):
    return (daily_returns.mean() * 252 - risk_free_rate) / (daily_returns.std() * (252 ** 0.5)) 