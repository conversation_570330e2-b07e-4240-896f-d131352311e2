from datetime import date, timedelta, datetime
import pandas as pd
from decimal import Decimal
from backtest.position import Position
import logging
from polygon import RESTClient
from dotenv import load_dotenv
import os
import pandas as pd
import numpy as np

# Get the logger
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Retrieve the Polygon API key from environment variables
POLYGON_API_KEY = os.getenv("POLYGON_API_KEY")
if not POLYGON_API_KEY:
    logger.error("POLYGON_API_KEY not set in environment variables.")
    # Optionally, raise an exception to halt execution
    # raise EnvironmentError("POLYGON_API_KEY not set in environment variables.")

# Initialize Polygon REST client globally
try:
    polygon_client = RESTClient(POLYGON_API_KEY)
except Exception as e:
    logger.error(f"Failed to initialize Polygon REST client: {e}")
    polygon_client = None  # Handle as per your application's requirement

def calculate_rsi(df, period=14):
    """Calculate Relative Strength Index (RSI)."""
    delta = df['close'].diff(1)  # Price change
    
    gain = np.where(delta > 0, delta, 0)  # Gains
    loss = np.where(delta < 0, -delta, 0)  # Losses

    avg_gain = pd.Series(gain).rolling(window=period, min_periods=1).mean()
    avg_loss = pd.Series(loss).rolling(window=period, min_periods=1).mean()

    rs = avg_gain / (avg_loss + 1e-10)  # Avoid division by zero
    rsi = 100 - (100 / (1 + rs))
    
    return rsi

def run_naked_put_backtest(df, screener_id, initial_capital=10000):
    # Ensure the DataFrame is sorted by date
    df = df.sort_index()
    
    # Initialize variables
    capital = initial_capital
    cumulative_profit = 0
    
    # Compute overall statistics
    ma10 = df['MA10'].iloc[-1]
    ma50 = df['MA50'].iloc[-1]
    rsi = df['RSI'].iloc[-1]
    
    # Strategy evaluation
    if ma10 > ma50 and rsi < 70:
        status = 'Active'
        daily_profit = df['close'].pct_change().sum() * initial_capital  # Simulated profit/loss based on price change
    elif ma10 < ma50 or rsi < 30:
        status = 'Expired'
        daily_profit = df['close'].pct_change().sum() * initial_capital  # Simulated profit/loss based on price change
    else:
        status = 'Assigned'
        daily_profit = 0
    
    capital += daily_profit
    cumulative_profit += daily_profit
    
    # Prediction placeholders (Modify if a predictive model is available)
    prediction_opinion = None
    prediction_probability = None
    
    # Store results in a single object
    result = {
        'screener_id': screener_id,
        'date': str(datetime.now().date()),
        'daily_profit': round(daily_profit, 2),
        'cumulative_profit': round(cumulative_profit, 2),
        'capital': round(capital, 2),
        'status': status,
        'created_at': str(datetime.now()),
        'prediction_opinion': prediction_opinion,
        'prediction_probability': prediction_probability,
        'updated_at': None,
        'ma10': round(ma10, 2),
        'ma50': round(ma50, 2),
        'rsi': round(rsi, 2)
    }
    
    return result

def run_covered_call_backtest(df, screener_id, initial_capital=10000):
    # Ensure the DataFrame is sorted by date
    df = df.sort_index()
    
    # Initialize variables
    capital = initial_capital
    cumulative_profit = 0
    
    # Compute overall statistics
    ma10 = df['MA10'].iloc[-1]
    ma50 = df['MA50'].iloc[-1]
    rsi = df['RSI'].iloc[-1]
    
    # Strategy evaluation
    if ma10 > ma50 and rsi < 70:
        status = 'Active'
        daily_profit = df['close'].pct_change().sum() * initial_capital  # Simulated profit/loss based on price change
    elif ma10 < ma50 or rsi < 30:
        status = 'Expired'
        daily_profit = df['close'].pct_change().sum() * initial_capital  # Simulated profit/loss based on price change
    else:
        status = 'Assigned'
        daily_profit = 0  # No profit if assigned
    
    capital += daily_profit
    cumulative_profit += daily_profit
    
    # Prediction placeholders
    prediction_opinion = None
    prediction_probability = None
    
    # Store results
    result = {
        'screener_id': screener_id,
        'date': str(datetime.now().date()),
        'daily_profit': round(daily_profit, 2),
        'cumulative_profit': round(cumulative_profit, 2),
        'capital': round(capital, 2),
        'status': status,
        'created_at': str(datetime.now()),
        'prediction_opinion': prediction_opinion,
        'prediction_probability': prediction_probability,
        'updated_at': None,
        'ma10': round(ma10, 2),
        'ma50': round(ma50, 2),
        'rsi': round(rsi, 2)
    }
    
    return result


def run_historical_backtest(symbol, screener_id, lookback_days):
    start_date = pd.Timestamp(date.today() - timedelta(days=lookback_days))
    end_date = datetime.now().strftime('%Y-%m-%d')
    logger.info(f"Fetching historical prices for {symbol} from {start_date} to {end_date}.")

    try:
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365 * 5)).strftime('%Y-%m-%d')

        response = polygon_client.get_aggs(
            ticker=symbol,
            multiplier=1,
            timespan='day',
            from_=start_date,
            to=end_date,
            limit=5000,
            adjusted=True
        )

        logger.debug(f'Raw response from Polygon API: {response[:3]}')

        if not response:
            logger.warning(f"No historical data found for symbol {symbol}.")
            return pd.DataFrame()

        data = [{
            'date': datetime.utcfromtimestamp(item.timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S'),
            'open': item.open,
            'high': item.high,
            'low': item.low,
            'close': item.close,
            'volume': item.volume
        } for item in response]

        df = pd.DataFrame(data)
        df['date'] = pd.to_datetime(df['date'])

        df['day_of_week'] = df['date'].dt.dayofweek
        df['month'] = df['date'].dt.month
        df['year'] = df['date'].dt.year

        df['sin_day_of_week'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['cos_day_of_week'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        df['sin_month'] = np.sin(2 * np.pi * df['month'] / 12)
        df['cos_month'] = np.cos(2 * np.pi * df['month'] / 12)

        df = df.drop(columns=['day_of_week', 'month'])

        # Calculate Moving Averages
        df['MA10'] = df['close'].rolling(window=10, min_periods=1).mean()
        df['MA50'] = df['close'].rolling(window=50, min_periods=1).mean()

        # Calculate RSI
        df['RSI'] = calculate_rsi(df, period=14)

        df = df.set_index('date', inplace=False)

        logger.info(f"DataFrame shape for {symbol}: {df.shape}")
        logger.info(f"First few rows of the DataFrame:\n{df.head()}")

        logger.info(f"Successfully fetched historical prices for {symbol}.")

        return run_naked_put_backtest(df, screener_id), run_covered_call_backtest(df, screener_id)

    except Exception as e:
        logger.error(f"Error in run_historical_backtest: {e}")
        return pd.DataFrame()

