from datetime import datetime, timedelta
from supabase import create_client, Client
from polygon import RESTClient
import os
from celery import Celery
from backtest.ml_performance import update_ml_performance

# Set environment variables or replace with actual strings
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_KEY")
POLYGON_API_KEY = os.getenv("POLYGON_API_KEY")

# Setup clients
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
polygon_client = RESTClient(POLYGON_API_KEY)
celery = Celery('tasks', broker='redis://localhost:6379/0')

yesterday = (datetime.now() - timedelta(days=1)).date()

def get_yesterday_close(symbol):
    try:
        aggs = polygon_client.get_aggs(
            ticker=symbol,
            multiplier=1,
            timespan='day',
            from_=yesterday,
            to=yesterday,
            adjusted=True,
            limit=1
        )
        return aggs[0].close if aggs else None
    except Exception as e:
        print(f"Error fetching price for {symbol}: {e}")
        return None

def evaluate_prediction(row, actual_close, strategy):
    strike = float(row['strike_price'])
    premium = float(row['premium'])
    predicted_range = float(row.get('predicted_profit_range') or 0)
    predicted_direction = row['predicted_direction']

    if strategy == "naked_put":
        # Expect stock to stay above strike
        actual_profit = premium if actual_close >= strike else (actual_close - strike + premium)
        actual_outcome = 'SUCCESS' if actual_close >= strike else 'FAILURE'
        prediction_accuracy = (actual_outcome == 'SUCCESS' and predicted_direction == 'UP')

    elif strategy == "covered_call":
        # Expect stock to stay below strike
        actual_profit = premium if actual_close <= strike else (premium - (actual_close - strike))
        actual_outcome = 'SUCCESS' if actual_close <= strike else 'FAILURE'
        prediction_accuracy = (actual_outcome == 'SUCCESS' and predicted_direction == 'DOWN')

    else:
        raise ValueError(f"Unknown strategy: {strategy}")

    profit_error_margin = abs(predicted_range - actual_profit) / (abs(predicted_range) or 1)

    return {
        "actual_outcome": actual_outcome,
        "actual_profit": round(actual_profit, 2),
        "prediction_accuracy": prediction_accuracy,
        "profit_error_margin": round(profit_error_margin, 4),
        "updated_at": datetime.utcnow().isoformat(),
        "validated_at": datetime.utcnow().isoformat()
    }

@celery.task(name='tasks.compare_result')
def compare_predicted_with_actual(batch_size=1000):
    from math import ceil

    for strategy in ["naked_put", "covered_call"]:
        table_name = f"{strategy}_forwardtest_results"
        offset = 0
        while True:
            # Step 1: Fetch a batch of records with pagination
            response = supabase.table(table_name)\
                .select("*")\
                .eq("expiration_date", str(yesterday))\
                .is_("prediction_accuracy", None)\
                .range(offset, offset + batch_size - 1)\
                .execute()

            records = response.data
            if not records:
                break  # No more records to process
            print(records[0])
            updates = []

            for row in records:
                symbol = row['symbol']
                actual_close = get_yesterday_close(symbol)
                if actual_close is None:
                    continue

                update_data = evaluate_prediction(row, actual_close, strategy=strategy)
                update_data['id'] = row['id']
                updates.append(update_data)

            # Step 2: Apply updates in batch (up to 1000 at a time)
            if updates:
                for i in range(0, len(updates), batch_size):
                    update_batch = updates[i:i + batch_size]
                    for update in update_batch:
                        record_id = update.get("id")
                        if record_id is not None:
                            # Remove 'id' from the update payload
                            update_data = {k: v for k, v in update.items() if k != "id"}
                            supabase.table(table_name) \
                                .update(update_data) \
                                .eq("id", record_id) \
                                .execute()
            offset += batch_size
    update_ml_performance()