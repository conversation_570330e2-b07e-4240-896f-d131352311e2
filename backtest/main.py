import logging
from decimal import Decimal
from backtest.position import Position
from backtest.historical import run_historical_backtest
from backtest.forward_test import run_forward_test
from backtest.metrics import calculate_backtest_metrics
from backtest.storage import store_backtest_results, store_forward_results

logger = logging.getLogger(__name__)

def run_complete_analysis(supabase_client, record):
    """
    Run both historical backtest and forward prediction
    """
    symbol, screener_id, option_type, exp_date, strike, premium, iv, otm_prob = record
    try:
        # 1. Run historical backtest (P&L calculation)
        naked_put_historical_results, covered_call_historical_results = run_historical_backtest(
            symbol,
            screener_id,
            lookback_days=180,
        )
        
        # 2. Store results
        store_backtest_results(
            supabase_client,
            option_type=option_type,
            naked_put_historical_results=naked_put_historical_results,
            covered_call_historical_results=covered_call_historical_results,
            forward_results='forward_results'
        )

        # 3. Run forward test (ML predictions)
        forward_tested_naked_put, forward_tested_covered_call = run_forward_test(
            symbol,
            screener_id,
            option_type,
            exp_date,
            strike,
            premium,
            iv,
            otm_prob
        )
        print(forward_tested_naked_put)
        # 4. Store forward test predictions
        store_forward_results(
            supabase_client,
            naked_put_future_results=forward_tested_naked_put,
            covered_call_future_results=forward_tested_covered_call,
            option_type=option_type
        )
        
    except Exception as e:
        logger.error(f"Analysis failed for {symbol}: {str(e)}")
        raise
