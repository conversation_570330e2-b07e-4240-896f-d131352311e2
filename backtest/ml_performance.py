import pandas as pd
from datetime import datetime
from supabase import create_client, Client
import os
from celery import Celery


SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_SERVICE_KEY")

# Setup clients
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
celery = Celery('tasks', broker='redis://localhost:6379/0')

# def fetch_all_records(supabase: Client, table_name: str, batch_size: int = 1000):
#     all_records = []
#     offset = 0

#     while True:
#         response = supabase.table(table_name).select(
#             'symbol, model_version, prediction_accuracy, predicted_profit_range, actual_profit'
#         ).range(offset, offset + batch_size - 1).execute()

#         data = response.data
#         if not data:
#             break

#         all_records.extend(data)
#         if len(data) < batch_size:
#             break

#         offset += batch_size

#     return pd.DataFrame(all_records)

# def fetch_data(supabase: Client):
#     df_covered_call = fetch_all_records(supabase, 'covered_call_forwardtest_results')
#     df_naked_put = fetch_all_records(supabase, 'naked_put_forwardtest_results')
#     return df_covered_call, df_naked_put

# Fetching data from both tables (covered_call_forwardtest_results and naked_put_forwardtest_results)
def fetch_data(supabase: Client):
    # Query the covered call forward test results
    covered_call_results = supabase.table('covered_call_forwardtest_results').select(
        'symbol, model_version, prediction_accuracy, predicted_profit_range, actual_profit'
    ).execute()

    # Query the naked put forward test results
    naked_put_results = supabase.table('naked_put_forwardtest_results').select(
        'symbol, model_version, prediction_accuracy, predicted_profit_range, actual_profit'
    ).execute()

    # Convert results to DataFrame for easy processing
    df_covered_call = pd.DataFrame(covered_call_results.data)
    df_naked_put = pd.DataFrame(naked_put_results.data)

    return df_covered_call, df_naked_put

# Calculate model performance (total predictions, correct predictions, accuracy, MAE)
def calculate_performance(df):
    total_predictions = len(df)
    correct_predictions = df['prediction_accuracy'].sum()
    accuracy_rate = correct_predictions / total_predictions if total_predictions > 0 else 0

    # Calculate mean absolute error (MAE)
    df['mae'] = abs(df['predicted_profit_range'] - df['actual_profit'])
    mean_absolute_error = df['mae'].mean()

    # Ensure the value is JSON-serializable (Supabase rejects NaN)
    if pd.isna(mean_absolute_error):
        mean_absolute_error = None

    return total_predictions, correct_predictions, accuracy_rate, mean_absolute_error

# Update model performance in the ml_model_performance table
def update_ml_model_performance(supabase: Client, symbol, model_version, strategy_type, 
                                 total_predictions, correct_predictions, accuracy_rate, mean_absolute_error):
    
    # Check if record exists for this symbol, model_version, and strategy_type
    response = supabase.table('ml_model_performance').select(
        'id'
    ).eq('symbol', symbol).eq('model_version', model_version).eq('strategy_type', strategy_type).execute()
    
    if response.data:
        record_id = response.data[0]['id']
        final_data = {
            'total_predictions': total_predictions,
            'correct_predictions': correct_predictions,
            'accuracy_rate': accuracy_rate,
            'mean_absolute_error': mean_absolute_error,
            'updated_at': str(datetime.now())
        }
        print(final_data)
        # Update the record
        update_response = supabase.table('ml_model_performance').update(final_data).eq('id', record_id).execute()
        
        if not update_response:
            print(f"Error updating/creating record for {symbol}: {update_response}")

    else:
        data = {
            'symbol': symbol,
            'model_version': model_version,
            'strategy_type': strategy_type,
            'total_predictions': total_predictions,
            'correct_predictions': correct_predictions,
            'accuracy_rate': accuracy_rate,
            'mean_absolute_error': mean_absolute_error ,
            'created_at': str(datetime.now()).split(' ')[0],
            'updated_at': str(datetime.now()).split(' ')[0],
            'lookback_period': total_predictions,
        }
        # Insert a new record
        insert_response = supabase.table('ml_model_performance').insert(data).execute()

        if not insert_response:
            print(f"Error updating/creating record for {symbol}: {insert_response}")

@celery.task(name='tasks.model_performance')
# Main function to fetch, calculate, and update model performance
def update_ml_performance():
    # Fetch data from both tables
    df_covered_call, df_naked_put = fetch_data(supabase)
    
    # Process and update the performance for the covered call models
    if not df_covered_call.empty:
        total_predictions, correct_predictions, accuracy_rate, mean_absolute_error = calculate_performance(df_covered_call)
        for symbol, model_version in df_covered_call[['symbol', 'model_version']].drop_duplicates().values:
            update_ml_model_performance(
                supabase, symbol, model_version, 'COVERED_CALL', 
                total_predictions, correct_predictions, accuracy_rate, mean_absolute_error
            )
    
    # Process and update the performance for the naked put models
    if not df_naked_put.empty:
        total_predictions, correct_predictions, accuracy_rate, mean_absolute_error = calculate_performance(df_naked_put)
        for symbol, model_version in df_naked_put[['symbol', 'model_version']].drop_duplicates().values:
            update_ml_model_performance(
                supabase, symbol, model_version, 'NAKED_PUT', 
                total_predictions, correct_predictions, accuracy_rate, mean_absolute_error
            )