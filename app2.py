from polygon import RESTClient

client = RESTClient('IG9YpkF9eRnoT_WD6in3dAqT7LTLFy6E')

# aggs = []
# for a in client.list_aggs(
#     "AAPL",
#     1,
#     "minute",
#     "2022-01-01",
#     "2023-02-03",
#     limit=50000,
# ):
#     aggs.append(a)

# print(aggs)

tickers = []
for t in client.list_tickers(market="stocks", type="CS", active="true", limit=1000):
    tickers.append(t)
print(tickers)