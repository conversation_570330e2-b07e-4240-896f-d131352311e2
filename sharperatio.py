import requests
import math
from datetime import datetime, timedelta

# Define the API key and endpoint
api_key = 'IG9YpkF9eRnoT_WD6in3dAqT7LTLFy6E'
base_url = 'https://api.polygon.io/v2'

# Define the parameters
symbol = 'MAXN'
expiration_date = '2024-07-19'
strike_price_1 = 1.00
strike_price_05 = 0.50
risk_free_rate = 0.02  # Risk-free rate (2% annualized)

# Function to get historical stock prices
def get_historical_stock_prices(symbol, from_date, to_date):
    url = f'{base_url}/aggs/ticker/{symbol}/range/1/day/{from_date}/{to_date}'
    response = requests.get(url, params={'apiKey': api_key})
    
    try:
        data = response.json()
    except requests.exceptions.JSONDecodeError:
        print(f"Error decoding JSON for historical prices of {symbol}")
        return None
    
    if 'results' in data:
        return data['results']
    else:
        print(f"No data found for historical prices of {symbol}")
        return None

# Function to get option premiums
def get_option_premium(symbol, expiration, strike_price, option_type):
    url = f'https://api.polygon.io/v3/reference/options/contracts/{symbol}/{expiration}/{option_type}/{strike_price}?apiKey={api_key}'
    response = requests.get(url)
    
    try:
        data = response.json()
    except requests.exceptions.JSONDecodeError:
        print(f"Error decoding JSON for {symbol} {strike_price} {option_type} expiring on {expiration}")
        return None
    
    if 'results' in data and len(data['results']) > 0:
        return data['results'][0]['last_price']
    else:
        print(f"No data found for {symbol} {strike_price} {option_type} expiring on {expiration}")
        return None

# Define the date range for historical data
to_date = datetime.now().strftime('%Y-%m-%d')
from_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')

# Get historical stock prices
historical_prices = get_historical_stock_prices(symbol, from_date, to_date)

if historical_prices is None:
    exit(1)

# Calculate the probabilities based on historical prices
total_days = len(historical_prices)
days_between_05_and_1 = sum(1 for day in historical_prices if strike_price_05 <= day['c'] <= strike_price_1)
days_below_05 = sum(1 for day in historical_prices if day['c'] < strike_price_05)
days_above_1 = total_days - days_between_05_and_1 - days_below_05

prob_p1 = days_above_1 / total_days
prob_p2 = days_between_05_and_1 / total_days
prob_p3 = days_below_05 / total_days

# Get the premiums
try:
    P1 = get_option_premium(symbol, expiration_date, strike_price_1, 'put')
    P05 = get_option_premium(symbol, expiration_date, strike_price_05, 'put')
    
    if P1 is None or P05 is None:
        raise ValueError("Failed to fetch option premiums.")
except ValueError as e:
    print(e)
    exit(1)

# Calculate Credit, Max Profit, and Max Loss
credit = P1 - P05
max_profit = credit
max_loss = (strike_price_1 - strike_price_05) - credit

# Calculate Expected Return (ER)
expected_return = (prob_p1 * max_profit) + (prob_p2 * credit) - (prob_p3 * max_loss)

# Calculate Standard Deviation (σ)
variance = (prob_p1 * (max_profit - expected_return)**2) + \
           (prob_p2 * (credit - expected_return)**2) + \
           (prob_p3 * (-max_loss - expected_return)**2)
standard_deviation = math.sqrt(variance)

# Calculate Sharpe Ratio (SR)
sharpe_ratio = (expected_return - risk_free_rate) / standard_deviation

# Display the results
print(f"Credit: {credit:.2f}")
print(f"Max Profit: {max_profit:.2f}")
print(f"Max Loss: {max_loss:.2f}")
print(f"Expected Return: {expected_return:.2f}")
print(f"Standard Deviation: {standard_deviation:.2f}")
print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
