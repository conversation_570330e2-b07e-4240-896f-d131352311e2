import pytest
from app import app  # Import your Flask app


# This fixture sets up the Flask test client
@pytest.fixture
def client():
    app.config['TESTING'] = True
    with app.test_client() as client:
        yield client

def test_homepage(client):
    response = client.get('/')
    assert response.status_code == 200
    
# Test for Industry Data API endpoint
def test_get_industry(client):
    response = client.get('/api/industry')
    assert response.status_code == 200
    data = response.get_json()
    assert isinstance(data, list)
    assert 'Ticker' in data[0]
    assert 'sector' in data[0]
    assert 'industry' in data[0]


# # Test for IV and Greeks Data API endpoint
# def test_get_iv_greeks(client):
#     response = client.get('/api/iv_greeks')
#     assert response.status_code == 200
#     data = response.get_json()
#     assert isinstance(data, list)
#     assert 'Ticker' in data[0]
#     assert 'IV' in data[0]
#     assert 'Delta' in data[0]


# # Test for OTM Strike Prices API endpoint
# def test_get_otm_strike(client):
#     response = client.get('/api/otm_strike')
#     assert response.status_code == 200
#     data = response.get_json()
#     assert isinstance(data, list)
#     assert 'Ticker' in data[0]
#     assert 'OTM_Strike' in data[0]
#     assert 'Premium' in data[0]


# # Test for Portfolio Performance API endpoint
# def test_get_portfolio_performance(client):
#     response = client.get('/api/portfolio_performance')
#     assert response.status_code == 200
#     data = response.get_json()
#     assert isinstance(data, list)


# # Test for Trade Signals API endpoint
# def test_get_trade_signals(client):
#     response = client.get('/api/trade_signals')
#     assert response.status_code == 200
#     data = response.get_json()
#     assert isinstance(data, list)


# # Test for Forward Testing API endpoint
# def test_get_forward_testing(client):
#     response = client.get('/api/forward_testing')
#     assert response.status_code == 200
#     data = response.get_json()
#     assert isinstance(data, list)


# # Test for Technical Indicators API endpoint
# def test_get_indicators(client):
#     response = client.get('/api/indicators')
#     assert response.status_code == 200
#     data = response.get_json()
#     assert isinstance(data, list)
#     assert 'RSI' in data[0]
#     assert 'MACD' in data[0]


# # Test for Bid-Ask Spread API endpoint
# def test_get_bid_ask_spread(client):
#     response = client.get('/api/bid_ask_spread')
#     assert response.status_code == 200
#     data = response.get_json()
#     assert isinstance(data, list)


# # Test for Backtesting API endpoint
# def test_get_backtesting(client):
#     response = client.get('/api/backtesting')
#     assert response.status_code == 200
#     data = response.get_json()
#     assert isinstance(data, list)
