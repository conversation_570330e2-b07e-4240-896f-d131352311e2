# tests/test_models.py

import unittest
from unittest.mock import MagicMock, patch
from models import train_and_save_model
import joblib

# class TestModels(unittest.TestCase):
    # @patch('models.joblib.dump')  # Mock joblib.dump to prevent actual file writes
    # @patch('models.save_model')   # Mock save_model to prevent actual saving
    # @patch('models.train_model')  # Mock train_model to control its output
    # @patch('models.prepare_dataset')  # Mock prepare_dataset to control its output
    # def test_train_and_save_model_success(self, mock_prepare, mock_train, mock_save, mock_joblib_dump):
    #     """
    #     Test that train_and_save_model successfully trains and saves the model and scaler.
    #     """
    #     # Mock dataset preparation to return X_train, X_test, y_train, y_test, scaler
    #     mock_X_train = MagicMock()
    #     mock_X_test = MagicMock()
    #     mock_y_train = MagicMock()
    #     mock_y_test = MagicMock()
    #     mock_scaler = MagicMock()
    #     mock_prepare.return_value = (mock_X_train, mock_X_test, mock_y_train, mock_y_test, mock_scaler)
        
    #     # Mock model training to return a mock model
    #     mock_model = MagicMock()
    #     mock_train.return_value = mock_model
        
    #     # Mock save_model to return True indicating successful save
    #     mock_save.return_value = True
        
    #     # Call the function with a valid symbol and a mock get_historical_prices_func
    #     result = train_and_save_model('AAPL', MagicMock())
        
    #     # Assertions to ensure all steps were called correctly
    #     self.assertTrue(result, "train_and_save_model should return True on successful execution.")
    #     mock_prepare.assert_called_once_with('AAPL', MagicMock())
    #     mock_train.assert_called_once_with(mock_X_train, mock_y_train)
    #     mock_save.assert_called_once_with(mock_model, 'AAPL')
    #     mock_joblib_dump.assert_called_once_with(mock_scaler, 'models/AAPL_scaler.joblib')

    # @patch('models.joblib.dump')  # Mock joblib.dump to prevent actual file writes
    # @patch('models.save_model')   # Mock save_model to prevent actual saving
    # @patch('models.train_model')  # Mock train_model to control its output
    # @patch('models.prepare_dataset')  # Mock prepare_dataset to control its output
    # def test_train_and_save_model_no_dataset(self, mock_prepare, mock_train, mock_save, mock_joblib_dump):
    #     """
    #     Test that train_and_save_model returns False when dataset preparation fails.
    #     """
    #     # Mock dataset preparation to return None indicating failure
    #     mock_prepare.return_value = None
        
    #     # Call the function with an invalid symbol and a mock get_historical_prices_func
    #     result = train_and_save_model('INVALID', MagicMock())
        
    #     # Assertions to ensure the function behaves correctly on failure
    #     self.assertFalse(result, "train_and_save_model should return False when dataset preparation fails.")
    #     mock_prepare.assert_called_once_with('INVALID', MagicMock())
    #     mock_train.assert_not_called()
    #     mock_save.assert_not_called()
    #     mock_joblib_dump.assert_not_called()

    # @patch('models.joblib.dump')  # Mock joblib.dump to prevent actual file writes
    # @patch('models.save_model')   # Mock save_model to control its behavior
    # @patch('models.train_model')  # Mock train_model to simulate training failure
    # @patch('models.prepare_dataset')  # Mock prepare_dataset to return dataset
    # def test_train_and_save_model_training_failure(self, mock_prepare, mock_train, mock_save, mock_joblib_dump):
    #     """
    #     Test that train_and_save_model returns False when model training fails.
    #     """
    #     # Mock dataset preparation to return X_train, X_test, y_train, y_test, scaler
    #     mock_X_train = MagicMock()
    #     mock_X_test = MagicMock()
    #     mock_y_train = MagicMock()
    #     mock_y_test = MagicMock()
    #     mock_scaler = MagicMock()
    #     mock_prepare.return_value = (mock_X_train, mock_X_test, mock_y_train, mock_y_test, mock_scaler)
        
    #     # Mock model training to return None indicating failure
    #     mock_train.return_value = None
        
    #     # Call the function with a valid symbol and a mock get_historical_prices_func
    #     result = train_and_save_model('AAPL', MagicMock())
        
    #     # Assertions to ensure the function behaves correctly on training failure
    #     self.assertFalse(result, "train_and_save_model should return False when model training fails.")
    #     mock_prepare.assert_called_once_with('AAPL', MagicMock())
    #     mock_train.assert_called_once_with(mock_X_train, mock_y_train)
    #     mock_save.assert_not_called()
    #     mock_joblib_dump.assert_not_called()

    # @patch('models.joblib.dump')  # Mock joblib.dump to simulate saving failure
    # @patch('models.save_model')   # Mock save_model to return True indicating successful save
    # @patch('models.train_model')  # Mock train_model to return a mock model
    # @patch('models.prepare_dataset')  # Mock prepare_dataset to return dataset
    # def test_train_and_save_model_scaler_save_failure(self, mock_prepare, mock_train, mock_save, mock_joblib_dump):
    #     """
    #     Test that train_and_save_model returns False when saving the scaler fails.
    #     """
    #     # Mock dataset preparation to return X_train, X_test, y_train, y_test, scaler
    #     mock_X_train = MagicMock()
    #     mock_X_test = MagicMock()
    #     mock_y_train = MagicMock()
    #     mock_y_test = MagicMock()
    #     mock_scaler = MagicMock()
    #     mock_prepare.return_value = (mock_X_train, mock_X_test, mock_y_train, mock_y_test, mock_scaler)
        
    #     # Mock model training to return a mock model
    #     mock_model = MagicMock()
    #     mock_train.return_value = mock_model
        
    #     # Mock save_model to return True indicating successful save
    #     mock_save.return_value = True
        
    #     # Mock joblib.dump to raise an exception simulating a save failure
    #     mock_joblib_dump.side_effect = Exception("Failed to save scaler.")
        
    #     # Call the function with a valid symbol and a mock get_historical_prices_func
    #     result = train_and_save_model('AAPL', MagicMock())
        
    #     # Assertions to ensure the function handles scaler save failure correctly
    #     self.assertFalse(result, "train_and_save_model should return False when saving scaler fails.")
    #     mock_prepare.assert_called_once_with('AAPL', MagicMock())
    #     mock_train.assert_called_once_with(mock_X_train, mock_y_train)
    #     mock_save.assert_called_once_with(mock_model, 'AAPL')
    #     mock_joblib_dump.assert_called_once_with(mock_scaler, 'models/AAPL_scaler.joblib')

# if __name__ == '__main__':
#     unittest.main()
