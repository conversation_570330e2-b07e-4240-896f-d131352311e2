# test_industry.py

import unittest
from unittest.mock import patch
from industry import get_industry_data

# class TestIndustryData(unittest.TestCase):

    # @patch('industry.requests.get')
    # def test_get_industry_data_success(self, mock_get):
    #     mock_response = {
    #         "Ticker": "AAPL",
    #         "sector": "Technology",
    #         "industry": "Consumer Electronics",
    #         "story": "Apple continues to innovate."
    #     }
    #     mock_get.return_value.status_code = 200
    #     mock_get.return_value.json.return_value = [mock_response]

    #     result = get_industry_data(['AAPL'])
    #     expected = [{
    #         'Ticker': 'AAPL',
    #         'sector': 'Technology',
    #         'industry': 'Consumer Electronics',
    #         'story': 'Apple continues to innovate.'
    #     }]
    #     self.assertEqual(result, expected)

    # @patch('industry.requests.get')
    # def test_get_industry_data_missing_keys(self, mock_get):
    #     mock_response = {
    #         "Ticker": "MSFT",
    #         # 'sector' is missing
    #         "industry": "Software",
    #         "story": "Microsoft expands its cloud services."
    #     }
    #     mock_get.return_value.status_code = 200
    #     mock_get.return_value.json.return_value = [mock_response]

    #     result = get_industry_data(['MSFT'])
    #     expected = [{
    #         'Ticker': 'MSFT',
    #         'sector': 'N/A',
    #         'industry': 'Software',
    #         'story': 'Microsoft expands its cloud services.'
    #     }]
    #     self.assertEqual(result, expected)

    # @patch('industry.requests.get')
    # def test_get_industry_data_api_failure(self, mock_get):
    #     mock_get.side_effect = Exception("API Failure")

    #     result = get_industry_data(['GOOGL'])
    #     self.assertEqual(result, [])

# if __name__ == '__main__':
#     unittest.main()
