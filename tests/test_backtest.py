import unittest
from unittest.mock import MagicMock
import pandas as pd
from backtest import train_models_for_screeners
from supabase import Client  # Import Client from supabase

class TestBacktestFunctions(unittest.TestCase):
    def setUp(self):
        # Mock Supabase client
        self.supabase_client = MagicMock()
        
        # Mock data for 'naked_put_screener' and 'covered_call_screener'
        self.supabase_client.table.return_value.select.return_value.execute.side_effect = [
            MagicMock(data=[{'id': 1, 'symbol': 'AAPL'}, {'id': 2, 'symbol': 'MSFT'}]),
            MagicMock(data=[{'id': 3, 'symbol': 'GOOGL'}, {'id': 4, 'symbol': 'TSLA'}])
        ]
        
        # Mock historical prices function
        self.get_historical_prices_func = MagicMock()
        self.get_historical_prices_func.return_value = pd.DataFrame({
            'MA10': [150, 151, 152],
            'MA50': [145, 146, 147],
            'RSI': [60, 62, 63],
            'target': [1, 0, 1]
        })
    
    # def test_train_models_for_screeners_success(self):
    #     response = train_models_for_screeners(self.supabase_client, self.get_historical_prices_func)
    #     self.assertEqual(response['status'], 'Model training process completed.')
    #     self.assertTrue(self.get_historical_prices_func.called)
    #     self.assertTrue(self.supabase_client.table.called)
    
    def test_train_models_for_screeners_no_symbols(self):
        # Mock empty screener tables
        self.supabase_client.table.return_value.select.return_value.execute.side_effect = [
            MagicMock(data=[]),
            MagicMock(data=[])
        ]
        response = train_models_for_screeners(self.supabase_client, self.get_historical_prices_func)
        self.assertEqual(response['status'], 'Model training process completed.')
    
    def test_train_models_for_screeners_exception(self):
        # Mock exception in fetching screener data
        self.supabase_client.table.return_value.select.return_value.execute.side_effect = Exception("Supabase error")
        response = train_models_for_screeners(self.supabase_client, self.get_historical_prices_func)
        self.assertEqual(response['status'], 'Failed to train models.')
        self.assertIn('error', response)

if __name__ == '__main__':
    unittest.main()
