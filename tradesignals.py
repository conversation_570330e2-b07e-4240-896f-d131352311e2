import pandas as pd
import yfinance as yf
import ta

# Define the stock symbol and time period
symbol = 'AAPL'  # Replace with your desired stock symbol
start_date = '2022-01-01'
end_date = '2023-10-01'

# Fetch historical price data
df = yf.download(symbol, start=start_date, end=end_date)

# Ensure no missing data
df.dropna(inplace=True)

# Calculate Technical Indicators
# Moving Averages
df['MA50'] = df['Close'].rolling(window=50).mean()

# RSI
rsi_indicator = ta.momentum.RSIIndicator(close=df['Close'], window=14)
df['RSI'] = rsi_indicator.rsi()

# MACD
macd_indicator = ta.trend.MACD(close=df['Close'])
df['MACD'] = macd_indicator.macd()
df['MACD_Signal'] = macd_indicator.macd_signal()
df['MACD_Hist'] = macd_indicator.macd_diff()

# Initialize Trade Signals
df['Signal'] = 0

# Generate Signals for Selling Naked Puts
df.loc[
    (df['Close'] > df['MA50']) &
    (df['MACD'] > df['MACD_Signal']) &
    (df['RSI'] >= 40) & (df['RSI'] <= 60),
    'Signal'
] = 1  # Signal to Sell Put

# Generate Signals for Writing Covered Calls
df.loc[
    (df['Close'] <= df['MA50']) &
    (df['MACD'] < df['MACD_Signal']) &
    (df['RSI'] > 70),
    'Signal'
] = -1  # Signal to Sell Call

# Display the dates and signals
trade_signals = df[df['Signal'] != 0][['Close', 'MA50', 'RSI', 'MACD', 'MACD_Signal', 'Signal']]
print(trade_signals)
