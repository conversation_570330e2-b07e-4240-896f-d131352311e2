import pandas as pd
import plotly.express as px
from polygon import RESTClient
from datetime import date

# Replace 'YOUR_API_KEY' with your actual Polygon.io API key
api_key = 'IG9YpkF9eRnoT_WD6in3dAqT7LTLFy6E'

# Step 1: Retrieve the universe of optionable stocks
def get_optionable_stocks(api_key):
    client = RESTClient(api_key)
    try:
        date_today = date.today().isoformat()
        resp = client.reference_tickers(date=date_today, active=True)
        optionable_tickers = [ticker['ticker'] for ticker in resp['tickers']]
        return optionable_tickers
    except Exception as e:
        print(f"Error fetching optionable stocks: {e}")
        return []
    finally:
        client.close()

# Step 2: Retrieve option chains
def get_option_chains(api_key, ticker):
    client = RESTClient(api_key)
    try:
        resp = client.options_chains(ticker)
        return resp['results']
    except Exception as e:
        print(f"Error fetching option chains for {ticker}: {e}")
        return []
    finally:
        client.close()

# Step 3: Calculate Bull Put Credit Spread
def calculate_bull_put_credit_spread(option_chain):
    spreads = []
    for option in option_chain:
        if option['option_type'] == 'put' and option['strike_price'] < option['underlying_price']:
            net_credit = option['bid_price'] - option['ask_price']
            buying_power_effect = (option['strike_price'] - option['underlying_price']) * 100 - net_credit * 100
            reward_risk_ratio = (net_credit * 100) / buying_power_effect if buying_power_effect != 0 else float('inf')
            spread = {
                'ticker': option['symbol'],
                'strike_price': option['strike_price'],
                'premium_net_credit': net_credit,
                'HTB': option.get('hard_to_borrow', False),
                'reward_risk_ratio': reward_risk_ratio
            }
            spreads.append(spread)
    return spreads

# Step 4: Filter Hard-to-Borrow stocks
def filter_hard_to_borrow(spreads):
    return [spread for spread in spreads if not spread['HTB']]

# Step 5: Find the highest premium net credit
def find_best_spreads(filtered_spreads):
    best_spreads = []
    for ticker, spreads in filtered_spreads.items():
        if spreads:
            best_spread = max(spreads, key=lambda x: x['premium_net_credit'])
            best_spreads.append(best_spread)
    return best_spreads

# Main execution
if __name__ == "__main__":
    try:
        # Step 1: Retrieve optionable stocks
        optionable_stocks = get_optionable_stocks(api_key)
        print(f"Retrieved {len(optionable_stocks)} optionable stocks")

        # Step 2: Retrieve option chains for each stock
        option_chains = {ticker: get_option_chains(api_key, ticker) for ticker in optionable_stocks}
        print("Retrieved option chains")

        # Step 3: Calculate Bull Put Credit Spreads for each option chain
        bull_put_spreads = {ticker: calculate_bull_put_credit_spread(option_chain) for ticker, option_chain in option_chains.items()}
        print("Calculated Bull Put Credit Spreads")

        # Step 4: Filter Hard-to-Borrow stocks from spreads
        filtered_spreads = {ticker: filter_hard_to_borrow(spreads) for ticker, spreads in bull_put_spreads.items()}
        print("Filtered Hard-to-Borrow stocks")

        # Step 5: Find the best spreads with highest premium net credit
        best_spreads = find_best_spreads(filtered_spreads)
        print(f"Found best spreads: {best_spreads}")

        # Step 6: Ensure that the DataFrame has the correct columns
        results_df = pd.DataFrame(best_spreads)
        print(f"Data Frame Columns: {results_df.columns}")
        if results_df.empty or 'premium_net_credit' not in results_df.columns or 'reward_risk_ratio' not in results_df.columns:
            raise ValueError("Data frame does not contain necessary columns or is empty")

        # Step 7: Display the results with Plotly
        fig = px.scatter(
            results_df,
            x='premium_net_credit',
            y='reward_risk_ratio',
            text='ticker',
            title='Bull Put Credit Spread Analysis for Optionable Stocks',
            labels={'premium_net_credit': 'Premium Net Credit', 'reward_risk_ratio': 'Reward/Risk Ratio (%)'},
            hover_data=['strike_price']
        )

        fig.update_traces(marker=dict(size=12, opacity=0.8),
                          selector=dict(mode='markers+text'))

        fig.show()

    except Exception as e:
        print(f"Error in main execution: {e}")
