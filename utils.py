import os
import logging
import pandas as pd
from datetime import datetime, date, timedelta
from polygon import RESTClient
from dotenv import load_dotenv
import numpy as np

# Get the logger
logger = logging.getLogger(__name__)

# Load environment variables from .env file
load_dotenv()

# Retrieve the Polygon API key from environment variables
POLYGON_API_KEY = os.getenv("POLYGON_API_KEY")
if not POLYGON_API_KEY:
    logger.error("POLYGON_API_KEY not set in environment variables.")
    # Optionally, raise an exception to halt execution
    # raise EnvironmentError("POLYGON_API_KEY not set in environment variables.")

# Initialize Polygon REST client globally
try:
    polygon_client = RESTClient(POLYGON_API_KEY)
except Exception as e:
    logger.error(f"Failed to initialize Polygon REST client: {e}")
    polygon_client = None  # Handle as per your application's requirement

def categorize_prob(prob):
    if 0.85 <= prob <= 1.0:
        return "Strong Buy"
    elif 0.7 <= prob < 0.85:
        return "Buy"
    elif 0.4 <= prob < 0.7:
        return "Hold"
    elif 0.21 <= prob < 0.4:
        return "Sell"
    elif 0.0 <= prob < 0.21:
        return "Strong Sell"
    else:
        return "Invalid Probability"

def is_valid_symbol(symbol, polygon_client):
    """
    Check if the symbol exists using Polygon.io's get_ticker_details method.

    Args:
        symbol (str): The stock symbol to validate.
        polygon_client: The Polygon.io client instance.

    Returns:
        bool: True if the symbol is valid, False otherwise.
    """
    try:
        # Ensure symbol is stripped of whitespace and is uppercase
        symbol = symbol.strip().upper()

        # Fetch ticker details using get_ticker_details method
        ticker_details = polygon_client.get_ticker_details(symbol)

        # Validate based on the structure of the TickerDetails object
        if ticker_details and hasattr(ticker_details, 'active'):
            # Check if the symbol is active
            if ticker_details.active:
                return True
            else:
                logger.warning(f"Symbol {symbol} is inactive.")
                return False
        else:
            logger.warning(f"Unexpected response format for symbol {symbol}: {ticker_details}")
            return False

    except Exception as e:
        logger.error(f"Error validating symbol {symbol}: {e}")
        return False

def get_historical_prices(symbol, start_date=None, end_date=None):
    logger.info(f"Fetching historical prices for {symbol} from {start_date} to {end_date}.")

    try:
        # Ensure dates are in the correct format
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365 * 5)).strftime('%Y-%m-%d')  # Default to last 5 years
        if not end_date:
            end_date = datetime.now().strftime('%Y-%m-%d')

        # Fetch aggregate data using Polygon.io API
        response = polygon_client.get_aggs(
            ticker=symbol,
            multiplier=1,
            timespan='day',
            from_=start_date,
            to=end_date,
            limit=5000,  # Adjust as needed
            adjusted=True
        )
        
        # Log part of the response for debugging
        logger.debug(f'Raw response from Polygon API: {response[:3]}')  # Log first 3 entries for review

        # If no data was returned, return an empty DataFrame
        if not response:
            logger.warning(f"No historical data found for symbol {symbol}.")
            return pd.DataFrame()

        # Extract relevant data from the response
        data = [{
            'date': datetime.utcfromtimestamp(item.timestamp / 1000).strftime('%Y-%m-%d %H:%M:%S'),  # Convert ms timestamp to datetime
            'open': item.open,
            'high': item.high,
            'low': item.low,
            'close': item.close,
            'volume': item.volume
        } for item in response]

        # Create DataFrame
        df = pd.DataFrame(data)

        # Convert 'date' column to datetime
        df['date'] = pd.to_datetime(df['date'])

        # Extract date components: day of the week, month, year
        df['day_of_week'] = df['date'].dt.dayofweek  # 0 = Monday, 6 = Sunday
        df['month'] = df['date'].dt.month  # 1 = January, 12 = December
        df['year'] = df['date'].dt.year

        # Apply cyclical encoding to 'day_of_week' and 'month'
        df['sin_day_of_week'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['cos_day_of_week'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        df['sin_month'] = np.sin(2 * np.pi * df['month'] / 12)
        df['cos_month'] = np.cos(2 * np.pi * df['month'] / 12)

        # Drop the original 'day_of_week' and 'month' columns since it's no longer needed
        df = df.drop(columns=['day_of_week', 'month'])  # Drop the original columns to avoid redundancy

        df = df.set_index('date', inplace=False)

        # Log the DataFrame shape and the first few rows to ensure it's populated correctly
        logger.info(f"DataFrame shape for {symbol}: {df.shape}")
        logger.info(f"First few rows of the DataFrame:\n{df.head()}")

        logger.info(f"Successfully fetched historical prices for {symbol}.")

        return df

    except Exception as e:
        logger.error(f"Error fetching historical prices for {symbol}: {e}")
        return pd.DataFrame()

def calculate_rsi(series, window=14):
    """
    Calculate the Relative Strength Index (RSI) for a given series.
    :param series: pandas Series of prices (e.g., 'close')
    :param window: Lookback period for RSI calculation
    :return: pandas Series containing the RSI values
    """
    delta = series.diff()

    # Calculate gains and losses
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    # Use rolling mean with min_periods=1 to avoid NaNs for small windows
    avg_gain = gain.rolling(window=window, min_periods=1).mean()
    avg_loss = loss.rolling(window=window, min_periods=1).mean()

    # Avoid division by zero (replace avg_loss == 0 with a small epsilon)
    avg_loss.replace(0, 1e-10, inplace=True)

    # Compute Relative Strength (RS) and RSI
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    return rsi

def insert_backtest_result(
    supabase_client,
    table_name,
    screener_id,
    date_,
    daily_profit,
    cumulative_profit,
    capital,
    status,
    prob,
    ma10=None,
    ma50=None,
    rsi=None
):
    """
    Inserts or updates a backtest result in the given table.
    """
    try:
        # Prepare the data to be inserted/updated
        data = {
            'screener_id': screener_id,
            'date': date_.isoformat(),
            'daily_profit': round(daily_profit, 2),
            'cumulative_profit': round(cumulative_profit, 2),
            'capital': round(capital, 2),
            'status': status,
            'prediction_opinion': categorize_prob(prob) if prob is not None else None,
            'prediction_probability': prob,
            'ma10': round(ma10, 2) if ma10 is not None else None,
            'ma50': round(ma50, 2) if ma50 is not None else None,
            'rsi': round(rsi, 2) if rsi is not None else None,
            'created_at': datetime.utcnow().isoformat()  # Set creation time
        }

        # Use upsert to insert or update the record based on (screener_id, date)
        response = supabase_client.table(table_name) \
            .upsert(data, on_conflict= 'screener_id, date') \
            .execute()

        # Log success message
        if response:
            logger.info(
                f"Upserted backtest result for screener_id {screener_id} on {date_} into table '{table_name}'."
            )

        # Debug: Log the full response
        logger.debug(f"Supabase Response: {response}")

        # Handle errors in the response
        if response and hasattr(response, 'error') and response.error:
            logger.error(f"Supabase Error: {response.error}")

    except Exception as e:
        logger.error(f"Exception during processing {table_name}: {e}")

def fetch_screener_data(supabase_client):
    """
    Fetch non-expired Naked Put and Covered Call screener current data from Supabase.
    """
    try:
        # Helper function to fetch all records in batches of 1000
        def fetch_all_records_in_batches(table_name):
            limit = 1000
            offset = 0
            all_data = []
            while True:
                response = supabase_client.table(table_name).select('*').range(offset, offset+limit-1).execute()
                data = response.data
                if not data or len(data) == 0:
                    logger.info(f"No more records to fetch from '{table_name}' at offset {offset}.")
                    break
                all_data.extend(data)
                logger.info(f"Fetched {len(data)} records from '{table_name}' starting at offset {offset}. Total so far: {len(all_data)}.")
                offset += limit
            return all_data

        # Fetch Naked Put Screener Data in batches of 1000
        naked_put_data = fetch_all_records_in_batches('naked_put_screener_current')
        naked_put_data = pd.DataFrame(naked_put_data)
        logger.info(f"Fetched {len(naked_put_data)} Naked Put records.")

        # Fetch Covered Call Screener Data in batches of 1000
        covered_call_data = fetch_all_records_in_batches('covered_call_screener_current')
        covered_call_data = pd.DataFrame(covered_call_data)
        logger.info(f"Fetched {len(covered_call_data)} Covered Call records.")

        return naked_put_data, covered_call_data
    except Exception as e:
        logger.error(f"Error fetching screener data: {e}")
        return pd.DataFrame(), pd.DataFrame()

def upsert_screened_tickers(supabase_client, symbol: str, accuracy: float):
    """
    Upsert the symbol and accuracy into the 'screened_tickers' table.
    If the symbol exists, updates it; otherwise, inserts it.
    """
    try:
        data = {
            'symbol': symbol,
            'model_accuracy': accuracy,
        }

        # Attempt to update the record
        update_response = supabase_client.table('screened_tickers').update(data).eq('symbol', symbol).execute()

        if update_response.data:
            logger.info(f"Successfully updated symbol {symbol} with accuracy {accuracy:.2f}.")
            return True

        # If no records were updated, attempt to insert
        insert_response = supabase_client.table('screened_tickers').insert(data).execute()

        if insert_response.data:
            logger.info(f"Successfully inserted symbol {symbol} with accuracy {accuracy:.2f} into Supabase.")
            return True
        else:
            logger.error(f"Failed to insert data into Supabase: {insert_response.data}")
            return False

    except Exception as e:
        logger.error(f"Error upserting symbol {symbol} to Supabase: {e}")
        return False

def get_active_symbols(supabase_client):
    # Helper function to fetch all records in batches of 1000
    def fetch_all_records_in_batches(table_name):
        limit = 1000
        offset = 0
        all_data = []
        while True:
            response = supabase_client.table(table_name).select('*').range(offset, offset+limit-1).execute()
            data = response.data
            if not data or len(data) == 0:
                logger.info(f"No more records to fetch from '{table_name}' at offset {offset}.")
                break
            all_data.extend(data)
            logger.info(f"Fetched {len(data)} records from '{table_name}' starting at offset {offset}. Total so far: {len(all_data)}.")
            offset += limit
        return all_data

    naked_put_data = fetch_all_records_in_batches('naked_put_screener_current')
    covered_call_data = fetch_all_records_in_batches('covered_call_screener_current')
    
    return [(symbol['symbol'], symbol['id'], 'naked_put', symbol['expiration_date'], symbol['strike'], symbol['bid'], symbol['iv'], symbol['otm_prob']) for symbol in naked_put_data] + [(symbol['symbol'], symbol['id'], 'covered_call', symbol['expiration_date'], symbol['strike'], symbol['bid'], symbol['iv'], symbol['otm_prob']) for symbol in covered_call_data]