#!/bin/bash
set -e  # Exit on error

# Install system dependencies
sudo apt-get update
sudo apt-get install -y \
    build-essential \
    wget \
    python3-dev \
    pkg-config \
    make \
    gcc

# Download and install TA-Lib from source
wget http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-src.tar.gz
tar -xvf ta-lib-0.4.0-src.tar.gz
cd ta-lib/
./configure
make
sudo make install

# Update the shared library cache
sudo ldconfig

# Create symbolic links
sudo ln -sf /usr/local/lib/libta_lib.so.0 /usr/local/lib/libta_lib.so
sudo ln -sf /usr/local/lib/libta_lib.so.0 /usr/lib/libta_lib.so.0
sudo ln -sf /usr/local/lib/libta_lib.so.0 /usr/lib/libta_lib.so

# Set environment variables
export TA_LIBRARY_PATH=/usr/local/lib
export TA_INCLUDE_PATH=/usr/local/include

# Clean up
cd ..
rm -rf ta-lib ta-lib-0.4.0-src.tar.gz

# Install Python packages
pip install --upgrade pip wheel setuptools
pip install numpy==1.24.3
LDFLAGS="-L/usr/local/lib" pip install ta-lib

# Install remaining requirements
pip install -r requirements.txt
