import yfinance as yf
import pandas as pd
import numpy as np
import talib
import plotly.graph_objects as go
from plotly.subplots import make_subplots

# Function to backtest Bull Put Credit Spread for given parameters
def backtest_bull_put_credit_spread(data, short_put_strike, long_put_strike, premium_received, vertical_positions):
    profits = []
    entry_prices = []
    exit_prices = []
    net_credits = []

    in_trade = False
    entry_price = 0
    exit_price = 0
    total_profit = 0
    trade_profit = 0

    for i in range(len(data) - 1):  # Loop through data to avoid index errors
        row = data.iloc[i]
        next_row = data.iloc[i + 1]

        if not in_trade and row['Close'] >= short_put_strike and row['Close'] <= long_put_strike:
            # Enter trade
            in_trade = True
            entry_price = next_row['Open']
            entry_prices.append((next_row.name, next_row['Low'] - 2))  # Arrow above entry point

        if in_trade:
            # Check exit conditions
            if next_row['Close'] <= short_put_strike or next_row['Close'] >= long_put_strike:
                # Exit trade and calculate profit
                in_trade = False
                exit_price = next_row['Open']
                exit_prices.append((next_row.name, next_row['High'] + 2))  # Arrow below exit point
                trade_profit = (entry_price - exit_price + premium_received) * vertical_positions
                profits.append(trade_profit)
                net_credits.append(premium_received)
                total_profit += trade_profit

    return profits, entry_prices, exit_prices, net_credits, total_profit

# Fetch historical data for MAXN
ticker = "MAXN"
data = yf.download(ticker, period="6mo", interval="1d")

# Define trade parameters
short_put_strike = 1.0
long_put_strike = 0.5
premium_received = 0.45
vertical_positions = 100

# Calculate technical indicators
data['RSI'] = talib.RSI(data['Close'], timeperiod=14)
data['ATR'] = talib.ATR(data['High'], data['Low'], data['Close'], timeperiod=14)

# Define breakout signals and crossovers
data['Breakout_Long'] = (data['Close'] > data['High'].shift(1)) & (data['Close'].shift(1) < data['High'].shift(1))
data['Breakout_Short'] = (data['Close'] < data['Low'].shift(1)) & (data['Close'].shift(1) > data['Low'].shift(1))
data['MACD'], data['MACD_signal'], _ = talib.MACD(data['Close'], fastperiod=12, slowperiod=26, signalperiod=9)
data['SMA_20'] = talib.SMA(data['Close'], timeperiod=20)
data['SMA_50'] = talib.SMA(data['Close'], timeperiod=50)

# Backtest Bull Put Credit Spread
profits, entry_points, exit_points, net_credits, total_profit = backtest_bull_put_credit_spread(data, short_put_strike, long_put_strike, premium_received, vertical_positions)

# Calculate Probability of Profit (PoP)
total_trades = len(profits)
if total_trades > 0:
    profitable_trades = len([p for p in profits if p > 0])
    PoP = profitable_trades / total_trades
else:
    PoP = 0.0

# Calculate cumulative profits over time
cumulative_profits = np.cumsum(profits)

# Create Plotly figure with subplots
fig = make_subplots(rows=4, cols=1, shared_xaxes=True, vertical_spacing=0.1,
                    subplot_titles=("Stock Price and Trade Signals", "MACD", "RSI", "ATR"))

# Plot candlestick chart with entry and exit points
fig.add_trace(go.Candlestick(x=data.index,
                             open=data['Open'],
                             high=data['High'],
                             low=data['Low'],
                             close=data['Close'],
                             name="Candlestick"),
              row=1, col=1)

# Add entry points arrows
for point in entry_points:
    fig.add_annotation(x=point[0], y=point[1], ax=0, ay=-40, showarrow=True, arrowhead=1, arrowsize=1, arrowwidth=2, arrowcolor='green')

# Add exit points arrows
for point in exit_points:
    fig.add_annotation(x=point[0], y=point[1], ax=0, ay=40, showarrow=True, arrowhead=2, arrowsize=1, arrowwidth=2, arrowcolor='red')

# Plot RSI
fig.add_trace(go.Scatter(x=data.index, y=data['RSI'], mode='lines', name='RSI', line=dict(color='blue')), row=3, col=1)

# Plot ATR
fig.add_trace(go.Scatter(x=data.index, y=data['ATR'], mode='lines', name='ATR', line=dict(color='orange')), row=4, col=1)

# Add breakout signals
fig.add_trace(go.Scatter(x=data.index[data['Breakout_Long']], y=data['Close'][data['Breakout_Long']],
                         mode='markers', marker=dict(symbol='triangle-up', color='green', size=8),
                         name='Breakout Long', showlegend=True), row=1, col=1)

fig.add_trace(go.Scatter(x=data.index[data['Breakout_Short']], y=data['Close'][data['Breakout_Short']],
                         mode='markers', marker=dict(symbol='triangle-down', color='red', size=8),
                         name='Breakout Short', showlegend=True), row=1, col=1)

# Add MACD and Signal Line
fig.add_trace(go.Scatter(x=data.index, y=data['MACD'], mode='lines', name='MACD', line=dict(color='blue')), row=2, col=1)
fig.add_trace(go.Scatter(x=data.index, y=data['MACD_signal'], mode='lines', name='MACD Signal', line=dict(color='orange')), row=2, col=1)

# Add Moving Averages
fig.add_trace(go.Scatter(x=data.index, y=data['SMA_20'], mode='lines', name='SMA 20', line=dict(color='green')), row=1, col=1)
fig.add_trace(go.Scatter(x=data.index, y=data['SMA_50'], mode='lines', name='SMA 50', line=dict(color='red')), row=1, col=1)

# Plot Profit/Loss (Cumulative Profit) and Net Credit
fig.add_trace(go.Scatter(x=data.index[:-1], y=cumulative_profits, mode='lines', name='Cumulative Profit', line=dict(color='blue')), row=2, col=1)
fig.add_trace(go.Scatter(x=data.index[:-1], y=net_credits, mode='lines', name='Net Credit', line=dict(color='green')), row=2, col=1)

# Add Total Profit annotation
fig.add_annotation(xref='paper', yref='paper', x=0.05, y=0.85,
                   text=f'Total Profit: ${total_profit:.2f}',
                   showarrow=False,
                   font=dict(size=14),
                   bgcolor='white',
                   bordercolor='black',
                   borderwidth=1.5)

# Add Probability of Profit (PoP) annotation
fig.add_annotation(xref='paper', yref='paper', x=0.05, y=0.9,
                   text=f'Probability of Profit (PoP): {PoP:.2%}',
                   showarrow=False,
                   font=dict(size=14),
                   bgcolor='white',
                   bordercolor='black',
                   borderwidth=1.5)

# Update layout
fig.update_layout(title=f"Bull Put Credit Spread for {ticker}",
                  xaxis_rangeslider_visible=False,
                  legend=dict(
                      orientation="h",
                      yanchor="top",
                      y=1.08,
                      xanchor="right",
                      x=1
                  ),
                  margin=dict(l=50, r=50, t=120, b=50))  # Adjust margins to accommodate legend

# Show the dashboard
fig.show()
