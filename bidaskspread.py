import yfinance as yf
import pandas as pd

# Define the stock symbol
symbol = 'AAPL'  # Replace with your desired stock symbol

# Create a Ticker object
ticker = yf.Ticker(symbol)

# Get available options expiration dates
expirations = ticker.options

# Choose an expiration date
expiration = expirations[0]  # You can select any date from the list

# Fetch the options chain for the selected expiration date
options_chain = ticker.option_chain(expiration)

# Access calls and puts DataFrames
calls = options_chain.calls
puts = options_chain.puts

# Calculate Bid-Ask Spread for calls
calls['Bid-Ask Spread'] = calls['ask'] - calls['bid']

# Calculate Bid-Ask Spread for puts
puts['Bid-Ask Spread'] = puts['ask'] - puts['bid']

# Display bid-ask spread for call options
print("Call Options Bid-Ask Spread:")
print(calls[['contractSymbol', 'strike', 'bid', 'ask', 'Bid-Ask Spread']].head())

# Display bid-ask spread for put options
print("\nPut Options Bid-Ask Spread:")
print(puts[['contractSymbol', 'strike', 'bid', 'ask', 'Bid-Ask Spread']].head())

# from yahoo_fin import options
# import pandas as pd

# # Define the stock symbol
# symbol = 'AAPL'  # Replace with your desired stock symbol

# # Get available options expiration dates
# expirations = options.get_expiration_dates(symbol)

# # Choose an expiration date
# expiration = expirations[0]  # You can select any date from the list

# # Corrected function call: Use 'date' instead of 'expiration_date'
# options_chain = options.get_options_chain(symbol, date=expiration)

# # Access calls and puts DataFrames
# calls = options_chain['calls']
# puts = options_chain['puts']

# # Calculate Bid-Ask Spread for calls
# calls['Bid-Ask Spread'] = calls['Ask'] - calls['Bid']

# # Calculate Bid-Ask Spread for puts
# puts['Bid-Ask Spread'] = puts['Ask'] - puts['Bid']

# # Display bid-ask spread for call options
# print("Call Options Bid-Ask Spread:")
# print(calls[['Contract Name', 'Strike', 'Bid', 'Ask', 'Bid-Ask Spread']].head())

# # Display bid-ask spread for put options
# print("\nPut Options Bid-Ask Spread:")
# print(puts[['Contract Name', 'Strike', 'Bid', 'Ask', 'Bid-Ask Spread']].head())



