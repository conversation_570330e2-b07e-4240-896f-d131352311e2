from requests_html import HTMLSession
import pandas as pd

# Create an HTML Session
session = HTMLSession()

ticker_symbol = 'AAPL'
url = f'https://finance.yahoo.com/quote/{ticker_symbol}/options'

# Fetch the page
response = session.get(url)

# Render JavaScript (if necessary)
# Note: Rendering can be slow and may require additional setup
# response.html.render(timeout=20)

# Parse the options tables
tables = pd.read_html(response.html.html)

# The first table is calls, the second is puts
calls = tables[0]
puts = tables[1]

# Display the calls DataFrame
print(calls)
