## CoveredCalls-Backtesting app to create trade signals, evaluate scenarios: 

1. Backtesting - TA-Lib / pandas-ta: For calculating technical indicators.
2. Backesting - Scikit-learn: For running machine learning models and feature importance.
3. Backtesting - DuckDB: For running large-scale analytical queries on enriched datasets.
4. Backtesting - TimescaleDB: For time-series data storage and trend analysis.
5. Backtesting - Supabase: For managing enriched datasets and portfolio-specific analysis.

Turtle Trade Channel Indicator Entry Exit:
https://tos.mx/!dat6QxRq

Python reference for Turtle Trading: 
https://raposa.trade/blog/testing-turtle-trading-the-system-that-made-newbie-traders-millions/
