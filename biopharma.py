import requests
import pandas as pd
import plotly.express as px

# Replace 'YOUR_API_KEY' with your actual Polygon.io API key
api_key = 'IG9YpkF9eRnoT_WD6in3dAqT7LTLFy6E'
base_url = 'https://api.polygon.io/v3'

# Step 1: Retrieve the universe of biopharma stocks under $10
def get_biopharma_stocks():
    url = f'{base_url}/reference/tickers?type=CS&market=stocks&exchange=NASDAQ,NYSE&active=true&sort=ticker&order=asc&limit=1000&apiKey={api_key}'
    response = requests.get(url)
    if response.status_code != 200:
        raise ValueError(f"Error fetching tickers: {response.status_code} - {response.text}")
    data = response.json()
    biopharma_tickers = [item['ticker'] for item in data['results'] if 'pharma' in item['sic_description'].lower() and item['last_price'] < 10]
    return biopharma_tickers

biopharma_stocks = get_biopharma_stocks()
print(f"Retrieved {len(biopharma_stocks)} biopharma stocks under $10")

# Step 2: Retrieve option chains
def get_option_chains(ticker):
    url = f'{base_url}/options/chains/{ticker}?apiKey={api_key}'
    response = requests.get(url)
    if response.status_code != 200:
        print(f"Error fetching option chains for {ticker}: {response.status_code} - {response.text}")
        return []
    data = response.json()
    return data['results']

option_chains = {ticker: get_option_chains(ticker) for ticker in biopharma_stocks}
print("Retrieved option chains")

# Step 3: Calculate Bull Put Credit Spread
def calculate_bull_put_credit_spread(option_chain):
    spreads = []
    for option in option_chain:
        if option['option_type'] == 'put' and option['strike_price'] < option['underlying_price']:
            spread = {
                'ticker': option['ticker'],
                'strike_price': option['strike_price'],
                'premium_net_credit': option['bid_price'] - option['ask_price'],  # Simplified calculation
                'POP': option['delta'],  # Simplified calculation, use your own method
                'HTB': option.get('hard_to_borrow', False)
            }
            spreads.append(spread)
    return spreads

bull_put_spreads = {ticker: calculate_bull_put_credit_spread(option_chain) for ticker, option_chain in option_chains.items()}
print("Calculated Bull Put Credit Spreads")

# Step 4: Filter Hard-to-Borrow stocks
def filter_hard_to_borrow(spreads):
    return [spread for spread in spreads if not spread['HTB']]

filtered_spreads = {ticker: filter_hard_to_borrow(spreads) for ticker, spreads in bull_put_spreads.items()}
print("Filtered Hard-to-Borrow stocks")

# Step 5: Find the highest premium net credit and Probability of Profit (POP)
def find_best_spreads(filtered_spreads):
    best_spreads = []
    for ticker, spreads in filtered_spreads.items():
        if spreads:
            best_spread = max(spreads, key=lambda x: (x['premium_net_credit'], x['POP']))
            best_spreads.append(best_spread)
    return best_spreads

best_spreads = find_best_spreads(filtered_spreads)
print(f"Found best spreads: {best_spreads}")

# Ensure that the DataFrame has the correct columns
results_df = pd.DataFrame(best_spreads)
print(f"Data Frame Columns: {results_df.columns}")
if results_df.empty or 'premium_net_credit' not in results_df.columns or 'POP' not in results_df.columns:
    raise ValueError("Data frame does not contain necessary columns or is empty")

# Step 6: Display the results with Plotly
fig = px.scatter(
    results_df,
    x='premium_net_credit',
    y='POP',
    text='ticker',
    title='Bull Put Credit Spread Analysis for Biopharma Stocks Under $10',
    labels={'premium_net_credit': 'Premium Net Credit', 'POP': 'Probability of Profit (POP)'},
    hover_data=['strike_price']
)

fig.update_traces(marker=dict(size=12, opacity=0.8),
                  selector=dict(mode='markers+text'))

fig.show()
