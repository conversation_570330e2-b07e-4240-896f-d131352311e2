import pandas as pd
import yfinance as yf
from datetime import datetime
import ta

# Define user watchlist of stocks
watchlist = ['AAPL', 'TSLA', 'MSFT']  # User's watchlist

# Define function to fetch upcoming option expiration dates
def get_next_expiration_dates(stock):
    # Fetch option expiration dates from yfinance
    ticker = yf.Ticker(stock)
    expiration_dates = ticker.options
    current_date = datetime.datetime.now().date()
    
    # Filter to get the next monthly expiration date
    next_expiration = None
    for date in expiration_dates:
        exp_date = datetime.datetime.strptime(date, '%Y-%m-%d').date()
        if exp_date > current_date:
            next_expiration = exp_date
            break
    return next_expiration

# Define function to generate OTM strike prices for puts and calls
def generate_otm_strike_prices(current_price, otm_percentage=0.05):
    otm_put_strike = current_price * (1 - otm_percentage)  # 5% below the current price
    otm_call_strike = current_price * (1 + otm_percentage)  # 5% above the current price
    return round(otm_put_strike, 2), round(otm_call_strike, 2)

# Function to calculate Probability of Profit (PoP) using Delta
def calculate_pop(delta):
    return round(1 - delta, 2) if delta is not None else None

# Function to choose the best OTM strike based on IV, Bid-Ask spread, and PoP
def choose_best_strike(options, option_type):
    best_option = None
    for _, option in options.iterrows():
        bid = option['bid']
        ask = option['ask']
        iv = option['impliedVolatility']  # Implied Volatility
        delta = option['delta'] if 'delta' in option else None  # Use delta for PoP

        # Ensure we have bid-ask spread and IV data
        if pd.isna(bid) or pd.isna(ask) or pd.isna(iv) or pd.isna(delta):
            continue
        
        # Calculate the bid-ask spread and PoP
        spread = ask - bid
        pop = calculate_pop(delta)
        
        # Set conditions for a good trade (tight spread, high PoP, decent IV)
        if spread <= 0.5 and iv >= 0.2 and pop >= 0.7:  # You can adjust these thresholds
            if best_option is None or (iv > best_option['iv'] and spread < best_option['spread']):
                best_option = {
                    'strike': option['strike'],
                    'iv': iv,
                    'spread': spread,
                    'pop': pop,
                    'premium': (bid + ask) / 2  # Mid-point premium
                }
    return best_option

# Function to fetch real-time option premiums from yfinance
import yfinance as yf
from datetime import datetime

# Assuming this is the function in question
def get_option_premium(stock, expiration_date, strike_price, option_type='put'):
    try:
        # Fetch the ticker and option chain
        ticker = yf.Ticker(stock)
        
        # Ensure expiration_date is valid and in correct format
        if isinstance(expiration_date, datetime):
            expiration_date = expiration_date.strftime('%Y-%m-%d')

        options_chain = ticker.option_chain(expiration_date)

        if option_type == 'put':
            options = options_chain.puts
        else:
            options = options_chain.calls

        # Find the row for the specified strike price
        option = options[options['strike'] == strike_price]
        
        if option.empty:
            raise ValueError(f"No option found for strike price {strike_price} on {expiration_date}")
        
        # Extract necessary data (example)
        premium = option['lastPrice'].values[0]
        iv = option['impliedVolatility'].values[0]
        best_strike = option['strike'].values[0]
        pop = option['inTheMoney'].values[0]  # Just an example, may not represent PoP

        return premium, best_strike, iv, pop
    
    except Exception as e:
        print(f"Error fetching option data for {stock} on {expiration_date}: {str(e)}")
        return None, None, None, None

# Function to fetch stock data, calculate technical indicators, and generate trade signals
def process_stock(stock):
    # Fetch historical stock data
    end_date = datetime.now().date()
    start_date = end_date - datetime.timedelta(days=365)  # 1 year of data
    df = yf.download(stock, start=start_date, end=end_date)

    # Calculate Technical Indicators
    df['MA50'] = df['Close'].rolling(window=50).mean()
    df['RSI'] = ta.momentum.RSIIndicator(close=df['Close'], window=14).rsi()
    macd_indicator = ta.trend.MACD(close=df['Close'])
    df['MACD'] = macd_indicator.macd()
    df['MACD_Signal'] = macd_indicator.macd_signal()
    
    # Remove any NaN values resulting from indicator calculations
    df.dropna(inplace=True)
    
    # Get the current price and the next expiration date
    current_price = df['Close'].iloc[-1]  # Corrected from [-1] to .iloc[-1]
    next_expiration = get_next_expiration_dates(stock)
    
    # Generate OTM strike prices
    otm_put_strike, otm_call_strike = generate_otm_strike_prices(current_price)
    
    # Generate Trade Signals for Cash Flow Strategy
    signal = None
    premium = None
    best_strike = None
    iv = None
    pop = None
    if df['Close'].iloc[-1] > df['MA50'].iloc[-1] and df['MACD'].iloc[-1] > df['MACD_Signal'].iloc[-1] and 40 <= df['RSI'].iloc[-1] <= 60:
        signal = 'Sell Naked Put'  # Bullish signal
        premium, best_strike, iv, pop = get_option_premium(stock, next_expiration, otm_put_strike, option_type='put')
    elif df['Close'].iloc[-1] < df['MA50'].iloc[-1] and df['MACD'].iloc[-1] < df['MACD_Signal'].iloc[-1] and df['RSI'].iloc[-1] > 70:
        signal = 'Sell Covered Call'  # Bearish signal
        premium, best_strike, iv, pop = get_option_premium(stock, next_expiration, otm_call_strike, option_type='call')

    # Return the result for the stock
    return {
        'Stock': stock,
        'Current Price': current_price,
        'Best Strike': best_strike,
        'IV': iv,
        'PoP': pop,
        'Premium': premium,
        'Signal': signal
    }

# Process the watchlist
def process_watchlist(watchlist):
    results = []
    for stock in watchlist:
        result = process_stock(stock)
        results.append(result)
    return pd.DataFrame(results)

# Run the watchlist analysis
result_df = process_watchlist(watchlist)

# Display the trade signals and opportunities
print(result_df)
