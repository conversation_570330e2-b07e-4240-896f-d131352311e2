# # backtest.py


# import pandas as pd
# from datetime import datetime, date, timedelta
# from logging_config import setup_logging

# from supabase import create_client, Client  # Import the Supabase Client
# import models
# from utils import (
#     is_valid_symbol,
#     get_historical_prices,
#     insert_backtest_result,
#     fetch_screener_data,
#     calculate_rsi,
#     categorize_prob
# )
# # from industry import get_industry_data
# from models import make_prediction, make_prediction_with_features, get_latest_features, train_and_save_model, TOP_FEATURES, load_models, load_model_for_symbol
# import models  
# import algorithm  # Import the run_algorithm function
# from concurrent.futures import ThreadPoolExecutor, as_completed


# import os
# import sys

# # Initialize logger and load global models
# logger = setup_logging(__name__)
# load_models()


# def train_models_for_screeners(supabase_client, get_historical_prices_func):
#     """
#     Train ML models for all symbols using pagination, 
#     but not used in daily P&L logic.
#     """
#     try:
#         def fetch_symbols_in_batches(table_name):
#             limit = 1000
#             offset = 0
#             all_symbols = []
#             while True:
#                 response = (
#                     supabase_client.table(table_name)
#                     .select('symbol')
#                     .range(offset, offset + limit - 1)
#                     .execute()
#                 )
#                 data = response.data
#                 if not data:
#                     break
#                 all_symbols.extend([rec['symbol'].upper() for rec in data])
#                 offset += limit
#             return all_symbols

#         naked_put_symbols = set(fetch_symbols_in_batches('naked_put_screener_current'))
#         covered_call_symbols = set(fetch_symbols_in_batches('covered_call_screener_current'))
#         all_symbols = naked_put_symbols.union(covered_call_symbols)

#         with ThreadPoolExecutor(max_workers=5) as executor:
#             futures = {
#                 executor.submit(
#                     train_and_save_model, sym, get_historical_prices_func, supabase_client
#                 ): sym for sym in all_symbols
#             }
#             for fut in as_completed(futures):
#                 symbol = futures[fut]
#                 try:
#                     success = fut.result()
#                     if not success:
#                         logger.warning(f"Model training failed for {symbol}.")
#                 except Exception as e:
#                     logger.error(f"Error training model for {symbol}: {e}")
#     except Exception as e:
#         logger.error(f"Error during bulk training: {e}")


# def train_models_for_symbol(symbol: str, supabase_client: Client):
#     """
#     Train ML model for a specific symbol, 
#     but not used for forced exit or daily P&L.
#     """
#     try:
#         success = train_and_save_model(symbol, get_historical_prices, supabase_client)
#         if not success:
#             logger.warning(f"Model training failed for {symbol}.")
#     except Exception as e:
#         logger.error(f"Error training model for {symbol}: {e}")


# def get_last_backtest(supabase_client, table_name, screener_id):
#     """
#     Retrieve the latest backtest record for screener_id (date, capital, cum_profit, status).
#     """
#     try:
#         response = (
#             supabase_client.table(table_name)
#             .select("*")
#             .eq("screener_id", screener_id)
#             .order("date.desc")
#             .limit(1)
#             .execute()
#         )
#         data = response.data
#         if not data:
#             return None
#         rec = data[0]
#         last_date = datetime.strptime(rec["date"], "%Y-%m-%d").date()
#         capital = float(rec["capital"])
#         cumulative_profit = float(rec["cumulative_profit"])
#         status = rec["status"]
#         return last_date, capital, cumulative_profit, status
#     except Exception as e:
#         logger.error(f"Error in get_last_backtest: {e}")
#         return None


# def short_put_intrinsic_value(strike: float, current_price: float) -> float:
#     """
#     For a short put, intrinsic value = max(0, strike - current_price) * 100.
#     Used for daily mark-to-market.
#     """
#     return max(0.0, strike - current_price) * 100


# def covered_call_intrinsic_value(strike: float, current_price: float) -> float:
#     """
#     For a covered call, intrinsic value = max(0, current_price - strike) * 100.
#     Used for daily mark-to-market.
#     """
#     return max(0.0, current_price - strike) * 100


# def backtest_naked_put(
#     supabase_client,
#     record,
#     hist_df,
#     start_date,
#     initial_capital,
#     initial_cumulative_profit,
#     initial_status
# ):
#     """
#     Perform daily mark-to-market backtest for a single Naked Put
#     without referencing ML signals for forced exit.
#     - Collect premium on first day
#     - Each day, calculate daily P&L as day-over-day change 
#       in (premium - intrinsic_value).
#     - Expire or assign as needed.
#     """
#     try:
#         symbol = record["symbol"]
#         train_models_for_symbol(symbol, supabase_client)
#         load_model_for_symbol(symbol)

#         strike = float(record["strike"])
#         premium = float(record["bid"])  
#         expiration = datetime.strptime(record["expiration_date"], "%Y-%m-%d").date()

#         capital = initial_capital
#         cumulative_profit = initial_cumulative_profit
#         status = initial_status

#         # Compute MAs, RSI if needed, but not used for forced trades
#         if "MA10" not in hist_df.columns:
#             hist_df["MA10"] = hist_df["close"].rolling(window=10, min_periods=1).mean()
#         if "MA50" not in hist_df.columns:
#             hist_df["MA50"] = hist_df["close"].rolling(window=50, min_periods=1).mean()
#         if "RSI" not in hist_df.columns:
#             hist_df["RSI"] = calculate_rsi(hist_df["close"])

#         start_ts = pd.to_datetime(start_date)
#         filtered = hist_df[hist_df.index >= start_ts]
#         if filtered.empty:
#             return

#         logger.info(f"Backtest Naked Put ID {record['id']} ({symbol}) from {start_date}.")

#         position_opened = False
#         prev_unrealized_pnl = 0.0

#         for ts, row in filtered.iterrows():
#             current_date = ts.date()
#             daily_profit = 0.0

#             if status == "Active":
#                 current_price = float(row["close"])
#                 if not position_opened:
#                     # Collect premium once
#                     daily_profit = premium * 100
#                     capital += daily_profit
#                     cumulative_profit += daily_profit
#                     position_opened = True

#                     # Mark-to-market after opening
#                     current_intrinsic = short_put_intrinsic_value(strike, current_price)
#                     current_unrealized = (premium * 100) - current_intrinsic
#                     prev_unrealized_pnl = current_unrealized
#                 else:
#                     # Recompute today's intrinsic => unrealized
#                     current_intrinsic = short_put_intrinsic_value(strike, current_price)
#                     current_unrealized = (premium * 100) - current_intrinsic
#                     daily_profit = current_unrealized - prev_unrealized_pnl
#                     prev_unrealized_pnl = current_unrealized

#                     capital += daily_profit
#                     cumulative_profit += daily_profit

#             elif status == "Assigned":
#                 # If assigned logic (omitted daily changes for simplicity)
#                 daily_profit = 0.0
#             elif status == "Expired":
#                 daily_profit = 0.0

#             insert_backtest_result(
#                 supabase_client,
#                 "naked_put_backtest_results",
#                 record["id"],
#                 current_date,
#                 daily_profit,
#                 cumulative_profit,
#                 capital,
#                 status,
#                 prediction_probability=0.0,  # ignoring ML-based daily probability
#                 ma10=row["MA10"],
#                 ma50=row["MA50"],
#                 rsi=row["RSI"],
#             )

#         # Handle expiration if still active
#         if status == "Active" and expiration <= date.today():
#             # No extra profit if worthless; if assigned, you'd do final logic
#             daily_profit = 0.0
#             capital += daily_profit
#             cumulative_profit += daily_profit
#             status = "Expired"
#             insert_backtest_result(
#                 supabase_client,
#                 "naked_put_backtest_results",
#                 record["id"],
#                 expiration,
#                 daily_profit,
#                 cumulative_profit,
#                 capital,
#                 status,
#                 prediction_probability=0.0,
#                 ma10=0.0,
#                 ma50=0.0,
#                 rsi=0.0,
#             )
#     except Exception as e:
#         exc_type, exc_obj, exc_tb = sys.exc_info()
#         fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
#         logger.error(
#             f"Error in backtest_naked_put: {e} in {fname} at line {exc_tb.tb_lineno}"
#         )
#         raise


# def backtest_covered_call(
#     supabase_client,
#     record,
#     hist_df,
#     start_date,
#     initial_capital,
#     initial_cumulative_profit,
#     initial_status
# ):
#     """
#     Perform daily mark-to-market backtest for a single Covered Call
#     without referencing ML signals for forced exit.
#     - Collect premium on first day
#     - Each day, daily P&L = day-over-day change in (premium - intrinsic_value).
#     - Expire or assign as needed.
#     """
#     try:
#         symbol = record["symbol"]
#         train_models_for_symbol(symbol, supabase_client)
#         load_model_for_symbol(symbol)

#         strike = float(record["strike"]) 
#         premium = float(record["bid"])
#         expiration = datetime.strptime(record["expiration_date"], "%Y-%m-%d").date()

#         capital = initial_capital
#         cumulative_profit = initial_cumulative_profit
#         status = initial_status

#         if "MA10" not in hist_df.columns:
#             hist_df["MA10"] = hist_df["close"].rolling(window=10, min_periods=1).mean()
#         if "MA50" not in hist_df.columns:
#             hist_df["MA50"] = hist_df["close"].rolling(window=50, min_periods=1).mean()
#         if "RSI" not in hist_df.columns:
#             hist_df["RSI"] = calculate_rsi(hist_df["close"])

#         start_ts = pd.to_datetime(start_date)
#         filtered = hist_df[hist_df.index >= start_ts]
#         if filtered.empty:
#             return

#         logger.info(f"Backtest Covered Call ID {record['id']} ({symbol}) from {start_date}.")

#         position_opened = False
#         prev_unrealized_pnl = 0.0

#         for ts, row in filtered.iterrows():
#             current_date = ts.date()
#             daily_profit = 0.0

#             if status == "Active":
#                 current_price = float(row["close"])
#                 if not position_opened:
#                     daily_profit = premium * 100
#                     capital += daily_profit
#                     cumulative_profit += daily_profit
#                     position_opened = True

#                     intrinsic = covered_call_intrinsic_value(strike, current_price)
#                     current_unrealized = (premium * 100) - intrinsic
#                     prev_unrealized_pnl = current_unrealized
#                 else:
#                     intrinsic = covered_call_intrinsic_value(strike, current_price)
#                     current_unrealized = (premium * 100) - intrinsic
#                     daily_profit = current_unrealized - prev_unrealized_pnl
#                     prev_unrealized_pnl = current_unrealized

#                     capital += daily_profit
#                     cumulative_profit += daily_profit

#             elif status == "Assigned":
#                 daily_profit = 0.0
#             elif status == "Expired":
#                 daily_profit = 0.0

#             insert_backtest_result(
#                 supabase_client,
#                 "covered_call_backtest_results",
#                 record["id"],
#                 current_date,
#                 daily_profit,
#                 cumulative_profit,
#                 capital,
#                 status,
#                 prediction_probability=0.0,  # ignoring ML signals
#                 ma10=row["MA10"],
#                 ma50=row["MA50"],
#                 rsi=row["RSI"],
#             )

#         if status == "Active" and expiration <= date.today():
#             daily_profit = 0.0
#             capital += daily_profit
#             cumulative_profit += daily_profit
#             status = "Expired"
#             insert_backtest_result(
#                 supabase_client,
#                 "covered_call_backtest_results",
#                 record["id"],
#                 expiration,
#                 daily_profit,
#                 cumulative_profit,
#                 capital,
#                 status,
#                 prediction_probability=0.0,
#                 ma10=0.0,
#                 ma50=0.0,
#                 rsi=0.0,
#             )
#     except Exception as e:
#         exc_type, exc_obj, exc_tb = sys.exc_info()
#         fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
#         logger.error(
#             f"Error in backtest_covered_call: {e} in {fname} at line {exc_tb.tb_lineno}"
#         )
#         raise


# def verify_and_log_columns(supabase_client):
#     """
#     Verify if required columns exist in screener tables.
#     Logs warnings if any columns are missing.
#     """
#     required_columns = {
#         "naked_put_screener": ["average_profit", "total_profit", "win_rate"],
#         "covered_call_screener": ["average_profit", "total_profit", "win_rate"],
#     }
#     try:
#         for table, columns in required_columns.items():
#             # Using Supabase's table description API if available, otherwise skipping
#             # Here, we'll assume a function 'pg_table_def' exists or implement accordingly
#             response = supabase_client.rpc("pg_table_def", {"table": table}).execute()
#             table_def = pd.DataFrame(response.data)
#             existing_columns = table_def["column_name"].tolist()
#             for column in columns:
#                 if column not in existing_columns:
#                     logger.warning(
#                         f"Column '{column}' is missing in table '{table}'. Please add it to avoid errors."
#                     )
#     except Exception as e:
#         logger.error(f"Error verifying columns: {e}")



# def run_backtest(supabase_client):
#     """
#     Main function to run the daily mark-to-market backtest
#     with no forced P&L from ML signals.
#     """
#     models.load_models()
#     logger.info("Starting backtesting process.")

#     naked_put_data, covered_call_data = fetch_screener_data(supabase_client)

#     # NAKED PUT
#     logger.info("Backtesting Naked Puts.")
#     for _, record in naked_put_data.iterrows():
#         symbol = record["symbol"]
#         if not is_valid_symbol(symbol, supabase_client.polygon_client):
#             logger.warning(f"Skipping invalid symbol {symbol}")
#             continue

#         upload_date = datetime.strptime(record["upload_date"], "%Y-%m-%d").date()
#         expiration = datetime.strptime(record["expiration_date"], "%Y-%m-%d").date()
#         today = date.today()

#         last_bt = get_last_backtest(supabase_client, "naked_put_backtest_results", record["id"])
#         if last_bt:
#             last_date, capital, cum_profit, status = last_bt
#             start_date = last_date + timedelta(days=1)
#         else:
#             start_date = upload_date
#             capital = 100000.0
#             cum_profit = 0.0
#             status = "Active"

#         end_date = min(expiration, today)
#         if start_date > end_date:
#             continue

#         hist_df = get_historical_prices(symbol, start_date, end_date)
#         if hist_df is not None and not hist_df.empty:
#             backtest_naked_put(
#                 supabase_client,
#                 record,
#                 hist_df,
#                 start_date,
#                 capital,
#                 cum_profit,
#                 status
#             )

#     # COVERED CALL
#     logger.info("Backtesting Covered Calls.")
#     for _, record in covered_call_data.iterrows():
#         symbol = record["symbol"]
#         if not is_valid_symbol(symbol, supabase_client.polygon_client):
#             logger.warning(f"Skipping invalid symbol {symbol}")
#             continue

#         upload_date = datetime.strptime(record["csv_download_date"], "%Y-%m-%d").date()
#         expiration = datetime.strptime(record["expiration_date"], "%Y-%m-%d").date()
#         today = date.today()

#         last_bt = get_last_backtest(supabase_client, "covered_call_backtest_results", record["id"])
#         if last_bt:
#             last_date, capital, cum_profit, status = last_bt
#             start_date = last_date + timedelta(days=1)
#         else:
#             start_date = upload_date
#             capital = 5000.0
#             cum_profit = 0.0
#             status = "Active"

#         end_date = min(expiration, today)
#         if start_date > end_date:
#             continue

#         hist_df = get_historical_prices(symbol, start_date, end_date)
#         if hist_df is not None and not hist_df.empty:
#             backtest_covered_call(
#                 supabase_client,
#                 record,
#                 hist_df,
#                 start_date,
#                 capital,
#                 cum_profit,
#                 status
#             )

#     logger.info("Backtesting process completed.")
#     # Optionally: algorithm.run_algorithm(), populate_advanced_screener(supabase_client), etc.



# def populate_advanced_screener(supabase_client):
#     """
#     Populate the advanced_screener table with PoP, IV, Sector, Industry, Story, ML Prediction, and calculate ml_ranking.
#     Handles duplicate entries by checking for existing records before inserting.
#     """
#     try:
#         # Fetch all non-expired screener_ids from both naked_put_screener_current and covered_call_screener_current
#         naked_put_records = (
#             supabase_client.table("naked_put_screener_current")
#             .select("id", "symbol", "iv")
#             .execute()
#             .data
#         )
#         covered_call_records = (
#             supabase_client.table("covered_call_screener_current")
#             .select("id", "symbol", "iv")
#             .execute()
#             .data
#         )

#         # Combine all records to fetch industry data in batch
#         combined_records = naked_put_records + covered_call_records
#         symbols = list(
#             {record["symbol"].upper() for record in combined_records}
#         )  # Ensure uppercase for consistency

#         # Fetch Sector, Industry, Story data
#         logger.info("Fetching Sector, Industry, and Story data for all symbols.")
#         # industry_data = get_industry_data(symbols)
#         # industry_df = pd.DataFrame(industry_data).set_index("Ticker")

#         # Prepare lists for inserts and updates
#         advanced_screener_entries = []
#         updates_needed = []

#         # Process Naked Puts
#         logger.info("Processing Naked Put records for Advanced Screener.")
#         for record in naked_put_records:
#             screener_id = record["id"]
#             symbol = record["symbol"].upper()  # Normalize symbol casing
#             iv = float(record.get("iv", 0.0))
#             pop = calculate_PoP(
#                 supabase_client, "naked_put_backtest_results", screener_id
#             )
#             # Get sequence_length and top_features
#             sequence_length = 10  # Or the value you used during training
#             top_features = TOP_FEATURES.get(symbol)
#             if not top_features:
#                 logger.error(f"Top features not found for {symbol}. Skipping.")
#                 continue

#             features = get_latest_features(
#                 symbol, get_historical_prices, sequence_length, top_features
#             )
#             prediction, prediction_prob = make_prediction(
#                 symbol, get_historical_prices, sequence_length
#             ) if features is not None else None
#             ml_ranking = calculate_ml_ranking(
#                 pop, iv, prediction if prediction is not None else 0
#             )

#             # Fetch Sector, Industry, Story
#             # sector, industry, story = fetch_industry_data(industry_df, symbol)

#             # Prepare data dictionary
#             data = {
#                 "screener_id": screener_id,
#                 "symbol": symbol,
#                 "strategy_type": "Naked Put",
#                 "probability_of_profit": pop,
#                 "implied_volatility": iv,
#                 "sector": 'N/A',
#                 "industry": None,
#                 "story": 'N/A',
#                 "ml_prediction": prediction_prob if prediction_prob is not None else "N/A",
#                 "ml_ranking": ml_ranking,
#                 "ml_opinion": categorize_prob(prediction_prob),
#                 "created_at": datetime.utcnow().isoformat(),
#             }

#             # Check for existing records and decide to update or insert
#             existing_record = (
#                 supabase_client.table("advanced_screener")
#                 .select("*")
#                 .eq("screener_id", screener_id)
#                 .execute()
#                 .data
#             )
#             if existing_record:
#                 updates_needed.append(data)  # Add to updates list
#             else:
#                 advanced_screener_entries.append(data)  # Add to insert list

#         # Process Covered Calls
#         logger.info("Processing Covered Call records for Advanced Screener.")
#         for record in covered_call_records:
#             screener_id = record["id"]
#             symbol = record["symbol"].upper()
#             iv = float(record.get("iv", 0.0))
#             pop = calculate_PoP(
#                 supabase_client, "covered_call_backtest_results", screener_id
#             )

#             # Get sequence_length and top_features
#             sequence_length = 10  # Or the value you used during training
#             top_features = TOP_FEATURES.get(symbol)
#             if not top_features:
#                 logger.error(f"Top features not found for {symbol}. Skipping.")
#                 continue

#             features = get_latest_features(
#                 symbol, get_historical_prices, sequence_length, top_features
#             )
#             prediction, prediction_prob = make_prediction(
#                 symbol, get_historical_prices, sequence_length
#             ) if features is not None else None
#             ml_ranking = calculate_ml_ranking(
#                 pop, iv, prediction if prediction is not None else 0
#             )

#             # Fetch Sector, Industry, Story
#             # sector, industry, story = fetch_industry_data(industry_df, symbol)

#             # Prepare data dictionary
#             data = {
#                 "screener_id": screener_id,
#                 "symbol": symbol,
#                 "strategy_type": "Covered Call",
#                 "probability_of_profit": pop,
#                 "implied_volatility": iv,
#                 "sector": 'N/A',
#                 "industry": None,
#                 "story": 'N/A',
#                 "ml_prediction": prediction_prob if prediction_prob is not None else "N/A",
#                 "ml_ranking": ml_ranking,
#                 "ml_opinion": categorize_prob(prediction_prob),
#                 "created_at": datetime.utcnow().isoformat(),
#             }

#             # Check for existing records and decide to update or insert
#             existing_record = (
#                 supabase_client.table("advanced_screener")
#                 .select("*")
#                 .eq("screener_id", screener_id)
#                 .execute()
#                 .data
#             )
#             if existing_record:
#                 updates_needed.append(data)  # Add to updates list
#             else:
#                 advanced_screener_entries.append(data)  # Add to insert list

#         # Perform batch insert for new records
#         if advanced_screener_entries:
#             insert_response = (
#                 supabase_client.table("advanced_screener")
#                 .insert(advanced_screener_entries)
#                 .execute()
#             )
#             if not insert_response:
#                 logger.error(
#                     f"Failed to insert advanced_screener entries: {insert_response.error}"
#                 )
#             else:
#                 inserted_count = len(insert_response.data)
#                 logger.info(
#                     f"Successfully inserted {inserted_count} new records into advanced_screener."
#                 )

#         # Perform updates for existing records
#         if updates_needed:
#             for update_data in updates_needed:
#                 update_response = (
#                     supabase_client.table("advanced_screener")
#                     .update(update_data)
#                     .eq("screener_id", update_data["screener_id"])
#                     .execute()
#                 )
#                 if not update_response:
#                     logger.error(
#                         f"Failed to update advanced_screener entry with screener_id {update_data['screener_id']}: {update_response.error}"
#                     )
#                 else:
#                     logger.info(
#                         f"Successfully updated advanced_screener entry with screener_id {update_data['screener_id']}."
#                     )

#     except Exception as e:
#         logger.error(f"Error populating advanced_screener table: {e}")


# # def fetch_industry_data(industry_df, symbol):
# #     """Fetch Sector, Industry, and Story for the given symbol."""
# #     if symbol in industry_df.index:
# #         sector = industry_df.at[symbol, "sector"]
# #         industry = industry_df.at[symbol, "industry"]
# #         story = industry_df.at[symbol, "story"]
# #     else:
# #         sector = "N/A"
# #         industry = "N/A"
# #         story = "N/A"
# #     return sector, industry, story


# def update_advanced_screener(supabase_client):
#     """
#     Update existing entries in advanced_screener table with latest PoP, IV, Sector, Industry, Story, ML Prediction, and ml_ranking.
#     """
#     try:
#         # Fetch all entries from advanced_screener
#         response = supabase_client.table("advanced_screener").select("*").execute()
#         advanced_screener_data = pd.DataFrame(response.data)

#         if advanced_screener_data.empty:
#             logger.info("No entries found in advanced_screener to update.")
#             return

#         # Get unique symbols to fetch industry data
#         symbols = list(
#             {row["symbol"] for index, row in advanced_screener_data.iterrows()}
#         )
#         logger.info(
#             "Fetching updated Sector, Industry, and Story data for all symbols."
#         )
#         # industry_data = get_industry_data(symbols)
#         # industry_df = pd.DataFrame(industry_data).set_index("Ticker")

#         for index, row in advanced_screener_data.iterrows():
#             screener_id = row["screener_id"]
#             symbol = row["symbol"]
#             strategy_type = row["strategy_type"]
#             print(strategy_type)

#             if strategy_type == "Naked Put":
#                 table_name = "naked_put_backtest_results"
#                 screener_table = "naked_put_screener_current"
#             elif strategy_type == "Covered Call":
#                 table_name = "covered_call_backtest_results"
#                 screener_table = "covered_call_screener_current"
#             else:
#                 logger.warning(
#                     f"Unknown strategy_type {strategy_type} for screener_id {screener_id}. Skipping."
#                 )
#                 continue

#             # Calculate PoP
#             pop = calculate_PoP(supabase_client, table_name, screener_id)

#             # Fetch IV
#             iv = calculate_average_IV(supabase_client, screener_table, screener_id)

#             # Prepare features and prediction
#             features = get_latest_features(symbol, get_historical_prices)
#             if features:
#                 prediction, prediction_prob = make_prediction(symbol, features)
#             else:
#                 prediction = None

#             # Calculate ml_ranking
#             ml_ranking = calculate_ml_ranking(
#                 pop, iv, prediction if prediction is not None else 0
#             )

#             # Fetch Sector, Industry, Story
#             # if symbol in industry_df.index:
#             #     sector = industry_df.at[symbol, "sector"]
#             #     industry = industry_df.at[symbol, "industry"]
#             #     story = industry_df.at[symbol, "story"]
#             # else:
#             # sector = "N/A"
#             # industry = "N/A"
#             # story = "N/A"

#             # Update the advanced_screener entry
#             update_data = {
#                 "PoP": pop,
#                 "IV": iv,
#                 # "sector": sector,
#                 # "industry": industry,
#                 # "story": story,
#                 "ml_prediction": prediction_prob if prediction_prob is not None else "N/A",
#                 "ml_ranking": ml_ranking,
#                 "ml_opinion": categorize_prob(prediction_prob),
#                 "created_at": datetime.utcnow().isoformat(),
#             }
#             response = (
#                 supabase_client.table("advanced_screener")
#                 .update(update_data)
#                 .eq("screener_id", screener_id)
#                 .execute()
#             )

#             if hasattr(response, "error") and response.error:
#                 logger.error(
#                     f"Failed to update Advanced Screener for screener_id {screener_id}: {response.error}"
#                 )
#             else:
#                 logger.info(
#                     f"Updated Advanced Screener for screener_id {screener_id} with PoP {pop}%, IV {iv}"  #, Sector {sector}, Industry {industry}."
#                 )

#     except Exception as e:
#         logger.error(f"Error updating advanced_screener table: {e}")


# def update_screener_with_backtest(supabase_client):
#     """
#     Update screener tables with aggregated backtest metrics and populate advanced_screener.
#     """
#     logger.info("Starting to update screener tables with backtest metrics.")
#     try:
#         # Update Naked Put Screener
#         naked_put_results = (
#             supabase_client.table("naked_put_backtest_results").select("*").execute()
#         )
#         naked_put_df = pd.DataFrame(naked_put_results.data)

#         if not naked_put_df.empty:
#             agg_naked_put = (
#                 naked_put_df.groupby("screener_id")
#                 .agg(
#                     average_profit=("daily_profit", "mean"),
#                     total_profit=("cumulative_profit", "max"),
#                     win_rate=("daily_profit", lambda x: (x > 0).mean()),
#                 )
#                 .reset_index()
#             )

#             for index, row in agg_naked_put.iterrows():
#                 # Cast the screener_id to int to avoid the "1.0" issue
#                 screener_id = int(row["screener_id"])

#                 response = (
#                     supabase_client.table("naked_put_screener_current")
#                     .update(
#                         {
#                             "average_profit": round(row["average_profit"], 2),
#                             "total_profit": round(row["total_profit"], 2),
#                             "win_rate": round(row["win_rate"], 2),
#                         }
#                     )
#                     .eq("id", row["screener_id"])
#                     .execute()
#                 )
#                 if hasattr(response, "error") and response.error:
#                     logger.error(
#                         f"Failed to update Naked Put Screener ID {row['screener_id']}: {response.error}"
#                     )
#                 elif hasattr(response, "status_code") and response.status_code == 200:
#                     logger.info(
#                         f"Updated Naked Put Screener ID {row['screener_id']} with metrics."
#                     )
#                 else:
#                     logger.warning(
#                         f"Update Naked Put Screener ID {row['screener_id']} returned an unexpected response: {response}"
#                     )

#         # Update Covered Call Screener
#         covered_call_results = (
#             supabase_client.table("covered_call_backtest_results").select("*").execute()
#         )
#         covered_call_df = pd.DataFrame(covered_call_results.data)

#         if not covered_call_df.empty:
#             agg_covered_call = (
#                 covered_call_df.groupby("screener_id")
#                 .agg(
#                     average_profit=("daily_profit", "mean"),
#                     total_profit=("cumulative_profit", "max"),
#                     win_rate=("daily_profit", lambda x: (x > 0).mean()),
#                 )
#                 .reset_index()
#             )

#             for index, row in agg_covered_call.iterrows():
#                 # Cast the screener_id to int to avoid the "1.0" issue
#                 screener_id = int(row["screener_id"])

#                 response = (
#                     supabase_client.table("covered_call_screener_current")
#                     .update(
#                         {
#                             "average_profit": round(row["average_profit"], 2),
#                             "total_profit": round(row["total_profit"], 2),
#                             "win_rate": round(row["win_rate"], 2),
#                         }
#                     )
#                     .eq("id", row["screener_id"])
#                     .execute()
#                 )
#                 if hasattr(response, "error") and response.error:
#                     logger.error(
#                         f"Failed to update Covered Call Screener ID {row['screener_id']}: {response.error}"
#                     )
#                 elif hasattr(response, "status_code") and response.status_code == 200:
#                     logger.info(
#                         f"Updated Covered Call Screener ID {row['screener_id']} with metrics."
#                     )
#                 else:
#                     logger.warning(
#                         f"Update Covered Call Screener ID {row['screener_id']} returned an unexpected response: {response}"
#                     )

#         # Populate Advanced Screener
#         populate_advanced_screener(supabase_client)

#         logger.info("Screener tables updated with backtest metrics.")
#     except Exception as e:
#         logger.error(f"Error updating screener tables: {e}")
