import yfinance as yf
import pandas as pd
import ta
import matplotlib.pyplot as plt

def calculate_indicators(symbol='AAPL', start='2022-01-01', end='2023-10-01'):
    # Fetch historical data
    df = yf.download(symbol, start=start, end=end)
    
    # Ensure no missing data
    df = df.dropna()
    
    # Calculate Moving Averages
    df['MA10'] = df['Close'].rolling(window=10).mean()
    df['MA50'] = df['Close'].rolling(window=50).mean()
    
    # Calculate RSI
    df['RSI'] = ta.momentum.RSIIndicator(close=df['Close'], window=14).rsi()
    
    # Calculate MACD
    macd_indicator = ta.trend.MACD(close=df['Close'])
    df['MACD'] = macd_indicator.macd()
    df['MACD_Signal'] = macd_indicator.macd_signal()
    df['MACD_Hist'] = macd_indicator.macd_diff()

    # Optionally plot (if needed later, we avoid plt.show())
    plt.figure(figsize=(14,7))
    plt.plot(df['Close'], label='Close Price')
    plt.plot(df['MA10'], label='MA10')
    plt.plot(df['MA50'], label='MA50')
    plt.title('Close Price and Moving Averages')
    plt.legend()
    # Save plot instead of showing
    plt.savefig('output.png')  # Saves the plot to a file instead of showing it
    
    # Return the last few rows as a dictionary (for JSON response)
    return df[['Close', 'MA10', 'MA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Hist']].tail().to_dict()

# Example call (this would not be executed when imported by Flask)
if __name__ == '__main__':
    print(calculate_indicators())


# # import pandas as pd
# # import yfinance as yf
# # import ta  # Technical Analysis library

# # # Define the stock symbol and time period
# # symbol = 'AAPL'  # Replace with your desired stock symbol
# # start = '2022-01-01'
# # end = '2023-10-01'

# # # Fetch historical price data using yfinance
# # df = yf.download(symbol, start=start, end=end)

# # # Calculate Moving Averages
# # df['MA10'] = df['Close'].rolling(window=10).mean()   # 10-day Moving Average
# # df['MA50'] = df['Close'].rolling(window=50).mean()   # 50-day Moving Average

# # # Calculate RSI
# # df['RSI'] = ta.momentum.RSIIndicator(df['Close'], window=14).rsi()

# # # Calculate MACD
# # macd = ta.trend.MACD(df['Close'])
# # df['MACD'] = macd.macd()
# # df['MACD_Signal'] = macd.macd_signal()
# # df['MACD_Hist'] = macd.macd_diff()

# # # Display the last few rows
# # print(df[['Close', 'MA10', 'MA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Hist']].tail())



# import yfinance as yf
# import pandas as pd
# import ta

# # Fetch historical data
# symbol = 'AAPL'
# df = yf.download(symbol, start='2022-01-01', end='2023-10-01')

# # Ensure no missing data
# df = df.dropna()

# # Calculate Moving Averages
# df['MA10'] = df['Close'].rolling(window=10).mean()
# df['MA50'] = df['Close'].rolling(window=50).mean()

# # Calculate RSI
# df['RSI'] = ta.momentum.RSIIndicator(close=df['Close'], window=14).rsi()

# # Calculate MACD
# macd_indicator = ta.trend.MACD(close=df['Close'])
# df['MACD'] = macd_indicator.macd()
# df['MACD_Signal'] = macd_indicator.macd_signal()
# df['MACD_Hist'] = macd_indicator.macd_diff()

# # Plotting (optional)
# import matplotlib.pyplot as plt

# # Plot Close Price and Moving Averages
# plt.figure(figsize=(14,7))
# plt.plot(df['Close'], label='Close Price')
# plt.plot(df['MA10'], label='MA10')
# plt.plot(df['MA50'], label='MA50')
# plt.title('Close Price and Moving Averages')
# plt.legend()
# # plt.show()

# # Display the last few rows
# print(df[['Close', 'MA10', 'MA50', 'RSI', 'MACD', 'MACD_Signal', 'MACD_Hist']].tail())
