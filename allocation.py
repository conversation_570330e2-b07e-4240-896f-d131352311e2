# Investment Allocation Calculator

def get_risk_tolerance():
    print("Please answer the following questions to assess your risk tolerance.")
    score = 0

    q1 = input("\n1. What is your investment horizon?\n"
               "a) Short-term (0-2 years)\n"
               "b) Medium-term (3-5 years)\n"
               "c) Long-term (5+ years)\n"
               "Your answer (a/b/c): ").lower()
    if q1 == 'a':
        score += 1
    elif q1 == 'b':
        score += 2
    elif q1 == 'c':
        score += 3

    q2 = input("\n2. How would you describe your investment knowledge?\n"
               "a) Beginner\n"
               "b) Intermediate\n"
               "c) Advanced\n"
               "Your answer (a/b/c): ").lower()
    if q2 == 'a':
        score += 1
    elif q2 == 'b':
        score += 2
    elif q2 == 'c':
        score += 3

    q3 = input("\n3. Which statement best describes your investment goals?\n"
               "a) Preserve capital with minimal risk\n"
               "b) Balanced growth and income\n"
               "c) Aggressive growth with higher risk\n"
               "Your answer (a/b/c): ").lower()
    if q3 == 'a':
        score += 1
    elif q3 == 'b':
        score += 2
    elif q3 == 'c':
        score += 3

    q4 = input("\n4. How would you react to a 20% decline in your investment value?\n"
               "a) Sell immediately to prevent further losses\n"
               "b) Hold and wait for recovery\n"
               "c) Buy more at the lower price\n"
               "Your answer (a/b/c): ").lower()
    if q4 == 'a':
        score += 1
    elif q4 == 'b':
        score += 2
    elif q4 == 'c':
        score += 3

    return score

def suggest_allocation(score):
    if score <= 5:
        print("\nRisk Tolerance: Low")
        print("Suggested Allocation:")
        print(" - LUMN Covered Calls: 80%")
        print(" - SGMO Investment: 20%")
    elif 6 <= score <= 9:
        print("\nRisk Tolerance: Moderate")
        print("Suggested Allocation:")
        print(" - LUMN Covered Calls: 50%")
        print(" - SGMO Investment: 50%")
    else:
        print("\nRisk Tolerance: High")
        print("Suggested Allocation:")
        print(" - LUMN Covered Calls: 20%")
        print(" - SGMO Investment: 80%")

def main():
    print("Welcome to the Investment Allocation Calculator.")
    score = get_risk_tolerance()
    suggest_allocation(score)
    print("\nNote: This is a simplified assessment. Consider consulting a financial advisor for personalized advice.")

if __name__ == "__main__":
    main()
