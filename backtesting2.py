# # Initialize capital and position tracking
# capital = 100000  # Starting capital
# positions = []
# portfolio = []

# # Iterate over the DataFrame
# for index, row in df.iterrows():
#     if row['Signal'] == 1:
#         # Sell cash-secured put
#         strike_price = row['Close'] * 0.95  # Example strike price at 5% discount
#         premium = 1.5  # Hypothetical premium per share
#         positions.append({
#             'Type': 'Put',
#             'Date': index,
#             'Strike': strike_price,
#             'Premium': premium,
#             'Assigned': False
#         })
#         capital += premium * 100  # Option contracts are typically for 100 shares
#     elif row['Signal'] == -1:
#         # Sell covered call
#         strike_price = row['Close'] * 1.05  # Example strike price at 5% premium
#         premium = 2.0  # Hypothetical premium per share
#         positions.append({
#             'Type': 'Call',
#             'Date': index,
#             'Strike': strike_price,
#             'Premium': premium,
#             'Assigned': False
#         })
#         capital += premium * 100
#     portfolio.append(capital)

# # Plot portfolio value over time
# import matplotlib.pyplot as plt

# plt.plot(df.index, portfolio)
# plt.title('Portfolio Value Over Time')
# plt.xlabel('Date')
# plt.ylabel('Capital ($)')
# plt.show()


import pandas as pd
import yfinance as yf
import matplotlib.pyplot as plt
import ta

# Step 1: Fetch historical data for a stock symbol (e.g., AAPL)
symbol = 'AAPL'
start_date = '2022-01-01'
end_date = '2023-10-01'

# Fetch historical price data using yfinance
df = yf.download(symbol, start=start_date, end=end_date)

# Ensure no missing data
df.dropna(inplace=True)

# Step 2: Calculate Technical Indicators
# Moving Average (50-day)
df['MA50'] = df['Close'].rolling(window=50).mean()

# RSI
df['RSI'] = ta.momentum.RSIIndicator(close=df['Close'], window=14).rsi()

# MACD
macd = ta.trend.MACD(close=df['Close'])
df['MACD'] = macd.macd()
df['MACD_Signal'] = macd.macd_signal()

# Step 3: Generate Trade Signals
df['Signal'] = 0
# Signal to Sell Naked Puts
df.loc[
    (df['Close'] > df['MA50']) &
    (df['MACD'] > df['MACD_Signal']) &
    (df['RSI'] >= 40) & (df['RSI'] <= 60),
    'Signal'
] = 1  # Signal to Sell Put

# Signal to Sell Covered Calls
df.loc[
    (df['Close'] <= df['MA50']) &
    (df['MACD'] < df['MACD_Signal']) &
    (df['RSI'] > 70),
    'Signal'
] = -1  # Signal to Sell Call

# Step 4: Ensure DataFrame is not empty and proceed with backtesting
if df.empty:
    print("DataFrame is empty. Cannot proceed with backtesting.")
else:
    # Initialize capital and position tracking
    capital = 100000  # Starting capital
    positions = []
    portfolio = []

    # Step 5: Iterate over the DataFrame to simulate trades
    for index, row in df.iterrows():
        if row['Signal'] == 1:
            # Sell cash-secured put
            strike_price = row['Close'] * 0.95  # Example strike price at 5% discount
            premium = 1.5  # Hypothetical premium per share
            positions.append({
                'Type': 'Put',
                'Date': index,
                'Strike': strike_price,
                'Premium': premium,
                'Assigned': False
            })
            capital += premium * 100  # Option contracts are typically for 100 shares
        elif row['Signal'] == -1:
            # Sell covered call
            strike_price = row['Close'] * 1.05  # Example strike price at 5% premium
            premium = 2.0  # Hypothetical premium per share
            positions.append({
                'Type': 'Call',
                'Date': index,
                'Strike': strike_price,
                'Premium': premium,
                'Assigned': False
            })
            capital += premium * 100
        portfolio.append(capital)

    # # Step 6: Plot portfolio value over time
    # plt.plot(df.index, portfolio)
    # plt.title('Portfolio Value Over Time')
    # plt.xlabel('Date')
    # plt.ylabel('Capital ($)')
    # # plt.show()

# Display the final capital after all trades
print(f"Final Capital: ${capital:.2f}")
