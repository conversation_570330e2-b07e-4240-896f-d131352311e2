from supabase import create_client, Client
from celery import Celery
import backtest
import algorithm
import os
import sys
from logging_config import setup_logging

# Supabase Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

celery = Celery('tasks', broker='redis://localhost:6379/0')

logger = setup_logging(__name__)

@celery.task(name='tasks.run_backtesting')
def run_backtesting():
    """
    Trigger backtesting process and update screener tables.
    """
    try:
        # backtest.verify_and_log_columns(supabase)
        
        backtest.run_backtest(supabase)
        
        logger.info("Starting to update screener tables.")
        backtest.update_screener_with_backtest(supabase)
        logger.info("Screener tables updated successfully.")

        logger.info("Populating advanced_screener with backtest results.")
        backtest.populate_advanced_screener(supabase)
        logger.info("Advanced_screener populated successfully.")

        # Call the ranking algorithm after backtesting and screener population    	   
        try:
            logger.info("Executing the ranking algorithm.")
            # algorithm.run_algorithm()
            logger.info("Ranking algorithm executed successfully.")
        except Exception as e:
            logger.error(f"Error executing ranking algorithm: {e}")
            logger.info("Backtesting and screener update completed successfully.")
        return {"status": "Backtesting and screener update completed successfully."}
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        logger.error(f"Backtesting or screener update failed: {e} in {fname} at line {exc_tb.tb_lineno}")
        logger.error(f"Backtesting or screener update failed: {e}")
        return {"status": "Backtesting or screener update failed.", "error": str(e)}
