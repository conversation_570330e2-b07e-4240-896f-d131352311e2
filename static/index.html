<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backtesting API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f4f4f4;
        }
        h1 {
            color: #333;
        }
        h2 {
            color: #555;
            margin-top: 40px;
        }
        h3 {
            color: #666;
            margin-top: 20px;
        }
        h4 {
            color: #666;
            margin-top: 15px;
        }
        p {
            font-size: 16px;
            line-height: 1.6;
        }
        pre {
            background-color: #f9f9f9;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow-x: auto;
        }
        code {
            color: #d63384;
        }
        .endpoint {
            font-family: monospace;
            background-color: #eee;
            padding: 5px;
            border-radius: 3px;
        }
        .response {
            margin-top: 10px;
            background-color: #fff;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
            white-space: pre-wrap;
            word-wrap: break-word;
            display: none; /* Hidden by default */
        }
        .toggle-button {
            margin-top: 10px;
            padding: 5px 10px;
            background-color: #007bff;
            color: #fff;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .toggle-button:hover {
            background-color: #0056b3;
        }
        table {
            border-collapse: collapse;
            width: 100%;
        }
        th, td {
            text-align: left;
            padding: 8px;
        }
        th {
            background-color: #007bff;
            color: white;
        }
        tr:nth-child(even) {background-color: #f2f2f2;}
        .form-group {
            margin-top: 10px;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            margin-top: 5px;
            box-sizing: border-box;
        }
        .submit-button {
            padding: 5px 10px;
            background-color: #28a745;
            color: #fff;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin-top: 10px;
        }
        .submit-button:hover {
            background-color: #1e7e34;
        }
    </style>
</head>
<body>
    <h1>Backtesting API</h1>
    <p>Welcome to the API documentation for the CoveredCalls Backtesting application. Below you'll find a comprehensive list of available API endpoints along with instructions on how to use them.</p>

    <h2>Run Backtesting</h2>
    <button class="toggle-button" onclick="runBacktestingSequence()">Run Backtesting</button>
    <div class="response" id="backtesting-sequence-response">Loading...</div>

    
    <!-- Add this where you want the logs to appear -->
    <div id="backtesting-sequence-log" style="height: 200px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; margin: 10px 0; font-family: monospace;"></div>

    <!-- Add this after the existing backtesting toggle button -->
    <div class="flex items-center justify-between p-4 bg-white rounded-lg shadow">
                <h3 class="text-lg font-medium text-gray-900">Update Rankings</h3>
                <p class="text-sm text-gray-500">Update advanced screener rankings without running full backtest</p>
            </div>
        </div>
        <div class="flex items-center">
            <button onclick="updateRankings()" class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                Update Now
            </button>
        </div>
    </div>

    <script>
    // Add this to your existing script section
    async function updateRankings() {
        try {
            const button = event.target;
            const originalText = button.textContent;
            button.textContent = 'Updating...';
            button.disabled = true;

            const response = await fetch('/api/update-rankings');
            const data = await response.json();

            if (response.ok) {
                showNotification('Rankings updated successfully', 'success');
            } else {
                showNotification(`Error: ${data.error}`, 'error');
            }
        } catch (error) {
            showNotification(`Error: ${error.message}`, 'error');
        } finally {
            button.textContent = originalText;
            button.disabled = false;
        }
    }
    </script>
    <!-- 1. Industry Data -->
    <h2>1. Industry Data Endpoint</h2>
    <p><strong>URL:</strong> <span class="endpoint">/api/industry</span></p>
    <p>This endpoint retrieves sector, industry, and story data for specified stock tickers.</p>
    
    <h3>Example Usage</h3>
    <pre><code>GET /api/industry?tickers=AAPL,MSFT,GOOGL</code></pre>
    <p>Retrieves industry data for Apple, Microsoft, and Google.</p>
    
    <div class="form-group">
        <label for="industry-tickers">Tickers (comma-separated):</label>
        <input type="text" id="industry-tickers" placeholder="e.g., AAPL, MSFT, GOOGL">
    </div>
    <button class="toggle-button" onclick="getIndustryData()">Run & Toggle Response</button>
    <div class="response" id="industry-data">Loading...</div>

    <!-- 2. Train Models -->
    <h2>2. Train Models Endpoint</h2>
    <p><strong>URL:</strong> <span class="endpoint">/api/train_models</span></p>
    <p>This endpoint trains machine learning models for all symbols present in the screener tables.</p>
    
    <h3>Example Usage</h3>
    <pre><code>POST /api/train_models</code></pre>
    <p>Triggers the training of ML models for all screener symbols.</p>
    
    <button class="toggle-button" onclick="trainModels()">Run & Toggle Response</button>
    <div class="response" id="train-models-response">Loading...</div>

    <!-- 3. Predict -->
    <h2>3. Predict Endpoint</h2>
    <p><strong>URL:</strong> <span class="endpoint">/api/predict</span></p>
    <p>This endpoint makes a prediction (Buy or Sell) for a given stock symbol based on provided features.</p>
    
    <h3>Example Usage</h3>
    <pre><code>POST /api/predict</code></pre>
    <p>Expects a JSON payload with 'symbol', 'MA10', 'MA50', and 'RSI'.</p>
    
    <div class="form-group">
        <label for="predict-symbol">Symbol:</label>
        <input type="text" id="predict-symbol" placeholder="e.g., AAPL">
    </div>
    <div class="form-group">
        <label for="predict-MA10">MA10:</label>
        <input type="number" step="0.01" id="predict-MA10" placeholder="e.g., 150.25">
    </div>
    <div class="form-group">
        <label for="predict-MA50">MA50:</label>
        <input type="number" step="0.01" id="predict-MA50" placeholder="e.g., 145.50">
    </div>
    <div class="form-group">
        <label for="predict-RSI">RSI:</label>
        <input type="number" step="0.01" id="predict-RSI" placeholder="e.g., 60.5">
    </div>
    <button class="submit-button" onclick="predict()">Submit Prediction</button>
    <div class="response" id="predict-response">Loading...</div>

    <!-- 4. Backtesting -->
    <h2>4. Backtesting Endpoint</h2>
    <p><strong>URL:</strong> <span class="endpoint">/api/backtesting</span></p>
    <p>This endpoint runs the backtesting process for all records in the Naked Put and Covered Call screeners and updates the screener tables.</p>
    
    <h3>Example Usage</h3>
    <pre><code>GET /api/backtesting</code></pre>
    <p>Initiates the backtesting process and updates screener tables with backtest metrics.</p>
    
    <button class="toggle-button" onclick="runBacktesting()">Run & Toggle Response</button>
    <div class="response" id="backtesting-response">Loading...</div>

    <!-- 5. Backtest Results - Naked Put -->
    <h2>5. Backtest Results - Naked Put Endpoint</h2>
    <p><strong>URL:</strong> <span class="endpoint">/api/backtest_results/naked_put/&lt;screener_id&gt;</span></p>
    <p>This endpoint retrieves backtest results for a specific Naked Put screener ID.</p>
    
    <h3>Example Usage</h3>
    <pre><code>GET /api/backtest_results/naked_put/1</code></pre>
    <p>Retrieves backtest results for Naked Put screener ID 1.</p>
    
    <div class="form-group">
        <label for="naked-put-screener-id">Screener ID:</label>
        <input type="number" id="naked-put-screener-id" placeholder="e.g., 1">
    </div>
    <button class="toggle-button" onclick="getNakedPutBacktestResults()">Run & Toggle Response</button>
    <div class="response" id="naked-put-backtest-results">Loading...</div>

    <!-- 6. Backtest Results - Covered Call -->
    <h2>6. Backtest Results - Covered Call Endpoint</h2>
    <p><strong>URL:</strong> <span class="endpoint">/api/backtest_results/covered_call/&lt;screener_id&gt;</span></p>
    <p>This endpoint retrieves backtest results for a specific Covered Call screener ID.</p>
    
    <h3>Example Usage</h3>
    <pre><code>GET /api/backtest_results/covered_call/2</code></pre>
    <p>Retrieves backtest results for Covered Call screener ID 2.</p>
    
    <div class="form-group">
        <label for="covered-call-screener-id">Screener ID:</label>
        <input type="number" id="covered-call-screener-id" placeholder="e.g., 2">
    </div>
    <button class="toggle-button" onclick="getCoveredCallBacktestResults()">Run & Toggle Response</button>
    <div class="response" id="covered-call-backtest-results">Loading...</div>

    <!-- 7. Populate Screener -->
    <h2>7. Populate Screener Endpoint</h2>
    <p><strong>URL:</strong> <span class="endpoint">/api/populate_screener</span></p>
    <p>This endpoint updates screener tables with aggregated backtest metrics such as average profit, total profit, and win rate.</p>
    
    <h3>Example Usage</h3>
    <pre><code>POST /api/populate_screener</code></pre>
    <p>Populates screener tables with calculated backtest metrics.</p>
    
    <button class="toggle-button" onclick="populateScreener()">Run & Toggle Response</button>
    <div class="response" id="populate-screener">Loading...</div>

    <!-- 8. Populate Advanced Screener -->
    <h2>8. Populate Advanced Screener Endpoint</h2>
    <p><strong>URL:</strong> <span class="endpoint">/api/populate_advanced_screener</span></p>
    <p>This endpoint populates the advanced screener table with Probability of Profit (PoP), Implied Volatility (IV), Sector, Industry, Story, and calculates the ranking score.</p>
    
    <h3>Example Usage</h3>
    <pre><code>POST /api/populate_advanced_screener</code></pre>
    <p>Populates the advanced screener table with calculated metrics.</p>
    
    <button class="toggle-button" onclick="populateAdvancedScreener()">Run & Toggle Response</button>
    <div class="response" id="populate-advanced-screener">Loading...</div>

    <!-- 9. Advanced Screener -->
    <h2>9. Advanced Screener Endpoint</h2>
    <p><strong>URL:</strong> <span class="endpoint">/api/advanced_screener</span></p>
    <p>This endpoint retrieves the advanced screener data, ranked by the ranking score in descending order.</p>
    
    <h3>Example Usage</h3>
    <pre><code>GET /api/advanced_screener</code></pre>
    <p>Retrieves advanced screener data sorted by ranking score.</p>
    
    <button class="toggle-button" onclick="getAdvancedScreener()">Run & Toggle Response</button>
    <div class="response" id="advanced-screener">Loading...</div>

    <!-- 7. Update Rankings -->
    <h2>7. Update Rankings Endpoint</h2>
    <p><strong>URL:</strong> <span class="endpoint">/api/update-rankings</span></p>
    <p>This endpoint updates the advanced screener rankings by:</p>
    <ul>
        <li>Updating screener tables with historical backtest metrics</li>
        <li>Calculating ML predictions for forward testing</li>
        <li>Computing final ranking scores</li>
    </ul>

    <h3>Example Usage</h3>
    <pre><code>GET /api/update-rankings</code></pre>
    <p>Updates rankings without running a full backtest process.</p>

    <button class="toggle-button" onclick="updateRankings()">Update Rankings</button>
    <div class="response" id="rankings-response">Loading...</div>

    <!-- JavaScript to Fetch and Display Data -->
    <script>
        // Function to toggle visibility of response sections
        function toggleVisibility(elementId) {
            const element = document.getElementById(elementId);
            if (element.style.display === "none" || element.style.display === "") {
                element.style.display = "block";
            } else {
                element.style.display = "none";
            }
        }

        // 1. Industry Data
        function getIndustryData() {
            const tickers = document.getElementById('industry-tickers').value;
            let url = '/api/industry';
            if (tickers) {
                url += `?tickers=${encodeURIComponent(tickers)}`;
            }
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('industry-data').textContent = JSON.stringify(data, null, 2);
                    toggleVisibility('industry-data');
                })
                .catch(error => {
                    document.getElementById('industry-data').textContent = 'Error: ' + error;
                    toggleVisibility('industry-data');
                });
        }

        // 2. Train Models
        function trainModels() {
            fetch('/api/train_models', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('train-models-response').textContent = JSON.stringify(data, null, 2);
                    toggleVisibility('train-models-response');
                })
                .catch(error => {
                    document.getElementById('train-models-response').textContent = 'Error: ' + error;
                    toggleVisibility('train-models-response');
                });
        }

        // 3. Predict
        function predict() {
            const symbol = document.getElementById('predict-symbol').value.trim();
            const MA10 = parseFloat(document.getElementById('predict-MA10').value);
            const MA50 = parseFloat(document.getElementById('predict-MA50').value);
            const RSI = parseFloat(document.getElementById('predict-RSI').value);

            if (!symbol || isNaN(MA10) || isNaN(MA50) || isNaN(RSI)) {
                alert('Please provide valid inputs for Symbol, MA10, MA50, and RSI.');
                return;
            }

            const payload = {
                symbol: symbol,
                MA10: MA10,
                MA50: MA50,
                RSI: RSI
            };

            fetch('/api/predict', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('predict-response').textContent = JSON.stringify(data, null, 2);
                    toggleVisibility('predict-response');
                })
                .catch(error => {
                    document.getElementById('predict-response').textContent = 'Error: ' + error;
                    toggleVisibility('predict-response');
                });
        }

        // 4. Backtesting
        function runBacktesting() {
            fetch('/api/backtesting')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('backtesting-response').textContent = JSON.stringify(data, null, 2);
                    toggleVisibility('backtesting-response');
                })
                .catch(error => {
                    document.getElementById('backtesting-response').textContent = 'Error: ' + error;
                    toggleVisibility('backtesting-response');
                });
        }

        // 5. Backtest Results - Naked Put
        function getNakedPutBacktestResults() {
            const screenerId = document.getElementById('naked-put-screener-id').value;
            if (!screenerId) {
                alert('Please provide a Screener ID.');
                return;
            }
            const url = `/api/backtest_results/naked_put/${screenerId}`;
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('naked-put-backtest-results').textContent = JSON.stringify(data, null, 2);
                    toggleVisibility('naked-put-backtest-results');
                })
                .catch(error => {
                    document.getElementById('naked-put-backtest-results').textContent = 'Error: ' + error;
                    toggleVisibility('naked-put-backtest-results');
                });
        }

        // 6. Backtest Results - Covered Call
        function getCoveredCallBacktestResults() {
            const screenerId = document.getElementById('covered-call-screener-id').value;
            if (!screenerId) {
                alert('Please provide a Screener ID.');
                return;
            }
            const url = `/api/backtest_results/covered_call/${screenerId}`;
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    document.getElementById('covered-call-backtest-results').textContent = JSON.stringify(data, null, 2);
                    toggleVisibility('covered-call-backtest-results');
                })
                .catch(error => {
                    document.getElementById('covered-call-backtest-results').textContent = 'Error: ' + error;
                    toggleVisibility('covered-call-backtest-results');
                });
        }

        // 7. Populate Screener
        function populateScreener() {
            fetch('/api/populate_screener', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('populate-screener').textContent = JSON.stringify(data, null, 2);
                    toggleVisibility('populate-screener');
                })
                .catch(error => {
                    document.getElementById('populate-screener').textContent = 'Error: ' + error;
                    toggleVisibility('populate-screener');
                });
        }

        // 8. Populate Advanced Screener
        function populateAdvancedScreener() {
            fetch('/api/populate_advanced_screener', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    document.getElementById('populate-advanced-screener').textContent = JSON.stringify(data, null, 2);
                    toggleVisibility('populate-advanced-screener');
                })
                .catch(error => {
                    document.getElementById('populate-advanced-screener').textContent = 'Error: ' + error;
                    toggleVisibility('populate-advanced-screener');
                });
        }

        // 9. Advanced Screener
        function getAdvancedScreener() {
            fetch('/api/advanced_screener')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('advanced-screener').textContent = JSON.stringify(data, null, 2);
                    toggleVisibility('advanced-screener');
                })
                .catch(error => {
                    document.getElementById('advanced-screener').textContent = 'Error: ' + error;
                    toggleVisibility('advanced-screener');
                });
        }

        function runBacktestingSequence() {
            const logElement = document.getElementById('backtesting-sequence-log');
            logElement.innerHTML = 'Starting backtesting sequence...<br>';

            // Helper function to append log messages
            function appendLog(message, isError = false) {
                const timestamp = new Date().toLocaleTimeString();
                const color = isError ? 'color: red;' : 'color: black;';
                logElement.innerHTML += `<span style="${color}">[${timestamp}] ${message}</span><br>`;
                logElement.scrollTop = logElement.scrollHeight; // Auto-scroll to bottom
            }

            // Sequential API calls with error handling
            fetch('/api/backtesting')
                .then(response => {
                    if (!response.ok) throw new Error(`Backtest failed: ${response.statusText}`);
                    return response.text();
                })
                .then(data => {
                    appendLog('Backtest completed successfully.');
                    return fetch('/api/populate_screener', { method: "POST" });
                })
                .catch(error => {
		    console.log(error);
                    appendLog(`Error during backtest: ${error.message}`, true);
                    throw error; // Propagate error
                })
                .then(response => {
                    if (!response.ok) throw new Error(`Screener population failed: ${response.statusText}`);
                    return response.json();
                })
                .then(data => {
                    appendLog('Screener populated successfully.');
                    return fetch('/api/populate_advanced_screener', { method: "POST" });
                })
                .catch(error => {
		    console.log(`Error for screener: ${error}`);
                    appendLog(`Error during screener population: ${error.message}`, true);
                    throw error;
                })
                .then(response => {
                    if (!response.ok) throw new Error(`Advanced screener population failed: ${response.statusText}`);
                    return response.json();
                })
                .then(data => {
                    appendLog('Advanced screener populated successfully.');
                    appendLog('Backtesting sequence completed!');
                })
                .catch(error => {
    		    console.log(`Error for advanced screener: ${error}`);
                    appendLog(`Error during advanced screener population: ${error.message}`, true);
                    appendLog('Backtesting sequence failed!', true);
                });
        }

    </script>
</body>
</html>
