name: Python application

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

permissions:
  contents: read

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python 3.10
      uses: actions/setup-python@v3
      with:
        python-version: "3.10"
    
    - name: Install TA-Lib C Library
      run: |
        sudo apt-get update
        sudo apt-get install -y build-essential
        wget https://github.com/ta-lib/ta-lib/releases/download/v0.6.4/ta-lib-0.6.4-src.tar.gz
        tar -xzf ta-lib-0.6.4-src.tar.gz
        cd ta-lib-0.6.4
        ./configure
        make
        sudo make install

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 pytest
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

    - name: Lint with flake8
      run: |
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics

    # Uncomment to run tests
    # - name: Test with pytest
    #   run: |
    #     pytest
    # - name: web
    #   run: gunicorn -w 4 -b 0.0.0.0:5000 wsgi:app
