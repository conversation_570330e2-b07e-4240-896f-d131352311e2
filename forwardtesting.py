# import pandas as pd
# import yfinance as yf
# import datetime
# import ta

# # Define user watchlist of stocks
# watchlist = ['AAPL', 'TSLA', 'MSFT']  # User's watchlist

# # Define function to fetch upcoming option expiration dates
# def get_next_expiration_dates(stock):
#     # Fetch option expiration dates from yfinance
#     ticker = yf.Ticker(stock)
#     expiration_dates = ticker.options
#     current_date = datetime.datetime.now().date()
    
#     # Filter to get the next monthly expiration date
#     next_expiration = None
#     for date in expiration_dates:
#         exp_date = datetime.datetime.strptime(date, '%Y-%m-%d').date()
#         if exp_date > current_date:
#             next_expiration = exp_date
#             break
#     return next_expiration

# # Define function to generate OTM strike prices for puts and calls
# def generate_otm_strike_prices(current_price, otm_percentage=0.05):
#     otm_put_strike = current_price * (1 - otm_percentage)  # 5% below the current price
#     otm_call_strike = current_price * (1 + otm_percentage)  # 5% above the current price
#     return round(otm_put_strike, 2), round(otm_call_strike, 2)

# # Function to fetch real-time option premiums from yfinance
# def get_option_premium(stock, expiration_date, strike_price, option_type='put'):
#     ticker = yf.Ticker(stock)
    
#     # Fetch the option chain for the given expiration date
#     options_chain = ticker.option_chain(expiration_date)
    
#     # Filter based on the option type (put or call) and strike price
#     if option_type == 'put':
#         options = options_chain.puts
#     else:
#         options = options_chain.calls
    
#     # Filter for the exact strike price
#     option = options[options['strike'] == strike_price]
    
#     if not option.empty:
#         bid_price = option['bid'].values[0]
#         ask_price = option['ask'].values[0]
#         premium = (bid_price + ask_price) / 2  # Mid-point premium
#         return premium
#     else:
#         return None  # If no option is found for the given strike

# # Function to fetch stock data, calculate technical indicators, and generate trade signals
# def process_stock(stock):
#     # Fetch historical stock data
#     end_date = datetime.datetime.now().date()
#     start_date = end_date - datetime.timedelta(days=365)  # 1 year of data
#     df = yf.download(stock, start=start_date, end=end_date)

#     # Calculate Technical Indicators
#     df['MA50'] = df['Close'].rolling(window=50).mean()
#     df['RSI'] = ta.momentum.RSIIndicator(close=df['Close'], window=14).rsi()
#     macd_indicator = ta.trend.MACD(close=df['Close'])
#     df['MACD'] = macd_indicator.macd()
#     df['MACD_Signal'] = macd_indicator.macd_signal()
    
#     # Remove any NaN values resulting from indicator calculations
#     df.dropna(inplace=True)
    
#     # Get the current price and the next expiration date
#     current_price = df['Close'][-1]
#     next_expiration = get_next_expiration_dates(stock)
    
#     # Generate OTM strike prices
#     otm_put_strike, otm_call_strike = generate_otm_strike_prices(current_price)
    
#     # Generate Trade Signals for Cash Flow Strategy
#     signal = None
#     if df['Close'][-1] > df['MA50'][-1] and df['MACD'][-1] > df['MACD_Signal'][-1] and 40 <= df['RSI'][-1] <= 60:
#         signal = 'Sell Naked Put'  # Bullish signal
#         premium = get_option_premium(stock, next_expiration, otm_put_strike, option_type='put')
#     elif df['Close'][-1] < df['MA50'][-1] and df['MACD'][-1] < df['MACD_Signal'][-1] and df['RSI'][-1] > 70:
#         signal = 'Sell Covered Call'  # Bearish signal
#         premium = get_option_premium(stock, next_expiration, otm_call_strike, option_type='call')
#     else:
#         premium = None

#     # Return the result for the stock
#     return {
#         'Stock': stock,
#         'Current Price': current_price,
#         'OTM Put Strike': otm_put_strike,
#         'OTM Call Strike': otm_call_strike,
#         'Next Expiration': next_expiration,
#         'Signal': signal,
#         'Premium': premium
#     }

# # Process the watchlist
# def process_watchlist(watchlist):
#     results = []
#     for stock in watchlist:
#         result = process_stock(stock)
#         results.append(result)
#     return pd.DataFrame(results)

# # Run the watchlist analysis
# result_df = process_watchlist(watchlist)

# # Display the trade signals and opportunities
# print(result_df)

import pandas as pd
import yfinance as yf
import datetime
import ta

# Define user watchlist of stocks
watchlist = ['AAPL', 'TSLA', 'MSFT']  # User's watchlist

# Define function to fetch upcoming option expiration dates
def get_next_expiration_dates(stock):
    # Fetch option expiration dates from yfinance
    ticker = yf.Ticker(stock)
    expiration_dates = ticker.options
    current_date = datetime.datetime.now().date()
    
    # Filter to get the next monthly expiration date
    next_expiration = None
    for date in expiration_dates:
        exp_date = datetime.datetime.strptime(date, '%Y-%m-%d').date()
        if exp_date > current_date:
            next_expiration = exp_date
            break
    return next_expiration

# Define function to validate expiration date
def validate_expiration(stock, expiration_date):
    ticker = yf.Ticker(stock)
    available_expirations = ticker.options
    if expiration_date not in available_expirations:
        print(f"Expiration {expiration_date} not available. Using the closest available expiration.")
        return available_expirations[0]  # Use the closest available expiration
    return expiration_date

# Define function to generate OTM strike prices for puts and calls
def generate_otm_strike_prices(current_price, otm_percentage=0.05):
    otm_put_strike = current_price * (1 - otm_percentage)  # 5% below the current price
    otm_call_strike = current_price * (1 + otm_percentage)  # 5% above the current price
    return round(otm_put_strike, 2), round(otm_call_strike, 2)

# Function to fetch real-time option premiums from yfinance
def get_option_premium(stock, expiration_date, strike_price, option_type='put'):
    ticker = yf.Ticker(stock)
    
    # Fetch the option chain for the given expiration date
    expiration_date = validate_expiration(stock, expiration_date)
    options_chain = ticker.option_chain(expiration_date)
    
    # Filter based on the option type (put or call) and strike price
    if option_type == 'put':
        options = options_chain.puts
    else:
        options = options_chain.calls
    
    # Filter for the exact strike price
    option = options[options['strike'] == strike_price]
    
    if not option.empty:
        bid_price = option['bid'].values[0]
        ask_price = option['ask'].values[0]
        premium = (bid_price + ask_price) / 2  # Mid-point premium
        return premium
    else:
        return None  # If no option is found for the given strike

# Function to fetch stock data, calculate technical indicators, and generate trade signals
def process_stock(stock):
    # Fetch historical stock data
    end_date = datetime.datetime.now().date()
    start_date = end_date - datetime.timedelta(days=365)  # 1 year of data
    df = yf.download(stock, start=start_date, end=end_date)

    # Calculate Technical Indicators
    df['MA50'] = df['Close'].rolling(window=50).mean()
    df['RSI'] = ta.momentum.RSIIndicator(close=df['Close'], window=14).rsi()
    macd_indicator = ta.trend.MACD(close=df['Close'])
    df['MACD'] = macd_indicator.macd()
    df['MACD_Signal'] = macd_indicator.macd_signal()
    
    # Remove any NaN values resulting from indicator calculations
    df.dropna(inplace=True)
    
    # Get the current price and the next expiration date
    current_price = df['Close'].iloc[-1]  # Corrected from [-1] to .iloc[-1]
    next_expiration = get_next_expiration_dates(stock)
    
    # Generate OTM strike prices
    otm_put_strike, otm_call_strike = generate_otm_strike_prices(current_price)
    
    # Generate Trade Signals for Cash Flow Strategy
    signal = None
    if df['Close'].iloc[-1] > df['MA50'].iloc[-1] and df['MACD'].iloc[-1] > df['MACD_Signal'].iloc[-1] and 40 <= df['RSI'].iloc[-1] <= 60:
        signal = 'Sell Naked Put'  # Bullish signal
        premium = get_option_premium(stock, next_expiration, otm_put_strike, option_type='put')
    elif df['Close'].iloc[-1] < df['MA50'].iloc[-1] and df['MACD'].iloc[-1] < df['MACD_Signal'].iloc[-1] and df['RSI'].iloc[-1] > 70:
        signal = 'Sell Covered Call'  # Bearish signal
        premium = get_option_premium(stock, next_expiration, otm_call_strike, option_type='call')
    else:
        premium = None

    # Return the result for the stock
    return {
        'Stock': stock,
        'Current Price': current_price,
        'OTM Put Strike': otm_put_strike,
        'OTM Call Strike': otm_call_strike,
        'Next Expiration': next_expiration,
        'Signal': signal,
        'Premium': premium
    }

# Process the watchlist
def process_watchlist(watchlist):
    results = []
    for stock in watchlist:
        result = process_stock(stock)
        results.append(result)
    return pd.DataFrame(results)

# Run the watchlist analysis
result_df = process_watchlist(watchlist)

# Display the trade signals and opportunities
print(result_df)

