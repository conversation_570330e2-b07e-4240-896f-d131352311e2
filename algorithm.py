# algorithm.py

import os
import sys
import logging
from supabase import create_client, Client
import pandas as pd
from dotenv import load_dotenv
from datetime import datetime  # Import datetime for timestamp handling

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s:%(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Load environment variables from .env file
load_dotenv()

# Environment variables for Supabase credentials
SUPABASE_URL = os.getenv('SUPABASE_URL')  # e.g., "https://xyzcompany.supabase.co"
SUPABASE_KEY = os.getenv('SUPABASE_KEY')  # Your Supabase API key

if not SUPABASE_URL or not SUPABASE_KEY:
    logging.error("Supabase credentials are not set in environment variables.")
    raise EnvironmentError("Supabase credentials are not set in environment variables.")

# Initialize Supabase client
try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
except Exception as e:
    logging.error(f"Failed to initialize Supabase client: {e}")
    raise e  # Propagate exception

def fetch_table(table_name: str) -> pd.DataFrame:
    """
    Fetch all records from a Supabase table and return as a pandas DataFrame,
    handling pagination for tables with more than 1000 records.
    """
    logging.info(f"Fetching data from table: {table_name}")
    batch_size = 1000
    data = []
    offset = 0

    while True:
        try:
            response = supabase.from_(table_name).select("*").range(offset, offset + batch_size - 1).execute()
        except Exception as e:
            logging.error(f"Exception occurred while fetching data from {table_name}: {e}")
            raise e  # Propagate exception
        
        # Check for errors in the response
        if hasattr(response, 'error') and response.error:
            logging.error(f"Error fetching data from {table_name}: {response.error}")
            raise Exception(f"Supabase error: {response.error}")
        
        fetched_data = response.data
        if not fetched_data:
            break  # No more data to fetch
        
        data.extend(fetched_data)
        logging.info(f"Fetched {len(fetched_data)} records from {table_name} (Offset: {offset})")
        
        if len(fetched_data) < batch_size:
            break  # Last batch fetched
        
        offset += batch_size

    return pd.DataFrame(data)

def run_algorithm():
    """
    Execute the ranking algorithm by fetching necessary data from Supabase,
    processing it, and updating the 'advanced_screener' table with ranking scores.
    """
    try:
        logging.info("Starting the ranking algorithm...")

        # Fetch all necessary tables with pagination
        advanced_screener = fetch_table("advanced_screener")
        naked_put_backtest = fetch_table("naked_put_backtest_results")
        covered_call_backtest = fetch_table("covered_call_backtest_results")
        naked_put_screener_current = fetch_table("naked_put_screener_current")
        covered_call_screener_current = fetch_table("covered_call_screener_current")
        barchart_opinion = fetch_table("barchart_opinion_current")
        opinion_event = fetch_table("opinion_event")

        # Store all DataFrames in a dictionary for easy access
        tables = {
            "advanced_screener": advanced_screener,
            "naked_put_backtest_results": naked_put_backtest,
            "covered_call_backtest_results": covered_call_backtest,
            "naked_put_screener_current": naked_put_screener_current,
            "covered_call_screener_current": covered_call_screener_current,
            "barchart_opinion_current": barchart_opinion,
            "opinion_event": opinion_event
        }

        # Define required columns for each table
        required_columns = {
            "advanced_screener": ["screener_id", "strategy_type"],
            "naked_put_backtest_results": ["screener_id", "cumulative_profit"],
            "covered_call_backtest_results": ["screener_id", "cumulative_profit"],
            "naked_put_screener_current": ["id", "symbol"],
            "covered_call_screener_current": ["id", "symbol"],
            "barchart_opinion_current": ["symbol", "overall_action", "overall_percentage"],
            "opinion_event": ["symbol", "csv_download_date", "opinion_percent", "previous_percent", "last_week_percent", "last_month_percent"]
        }

        # Check for missing columns in each table
        for table, columns in required_columns.items():
            if table not in tables:
                logging.error(f"Table '{table}' was not fetched successfully.")
                raise Exception(f"Missing table: {table}")
            
            missing_cols = set(columns) - set(tables[table].columns)
            if missing_cols:
                logging.error(f"Table '{table}' is missing columns: {missing_cols}")
                raise Exception(f"Missing columns in {table}: {missing_cols}")

        # Assign variables from the tables dictionary for clarity
        advanced_screener = tables["advanced_screener"]
        naked_put_backtest = tables["naked_put_backtest_results"]
        covered_call_backtest = tables["covered_call_backtest_results"]
        naked_put_screener_current = tables["naked_put_screener_current"]
        covered_call_screener_current = tables["covered_call_screener_current"]
        barchart_opinion = tables["barchart_opinion_current"]
        opinion_event = tables["opinion_event"]

        # Add 'strategy_type' based on table name
        if not naked_put_backtest.empty:
            naked_put_backtest = naked_put_backtest.copy()
            naked_put_backtest['strategy_type'] = 'Naked Put'
        else:
            logging.warning("naked_put_backtest_results table is empty.")

        if not covered_call_backtest.empty:
            covered_call_backtest = covered_call_backtest.copy()
            covered_call_backtest['strategy_type'] = 'Covered Call'
        else:
            logging.warning("covered_call_backtest_results table is empty.")

        # Merge backtest results with advanced_screener
        if not naked_put_backtest.empty and not advanced_screener.empty:
            naked_put_merged = pd.merge(
                naked_put_backtest,
                advanced_screener,
                on='screener_id',
                how='inner',
                suffixes=('_naked', '_advanced')
            )
        else:
            naked_put_merged = pd.DataFrame()
            logging.warning("No data to merge for Naked Put backtest results.")

        if not covered_call_backtest.empty and not advanced_screener.empty:
            covered_call_merged = pd.merge(
                covered_call_backtest,
                advanced_screener,
                on='screener_id',
                how='inner',
                suffixes=('_covered', '_advanced')
            )
        else:
            covered_call_merged = pd.DataFrame()
            logging.warning("No data to merge for Covered Call backtest results.")

        # Combine both strategies
        combined_backtest = pd.concat([naked_put_merged, covered_call_merged], ignore_index=True)

        if combined_backtest.empty:
            logging.error("No combined backtest data available for processing.")
            raise Exception("Combined backtest data is empty.")

        # Get symbols from screener tables
        naked_put_screener_current = naked_put_screener_current[['id', 'symbol']].rename(columns={'id': 'screener_id', 'symbol': 'symbol_naked'})
        covered_call_screener_current = covered_call_screener_current[['id', 'symbol']].rename(columns={'id': 'screener_id', 'symbol': 'symbol_covered'})

        combined_backtest = pd.merge(
            combined_backtest,
            naked_put_screener_current,
            on='screener_id',
            how='left'
        )
        combined_backtest = pd.merge(
            combined_backtest,
            covered_call_screener_current,
            on='screener_id',
            how='left'
        )

        # Handle symbols: Prefer symbol_naked, else symbol_covered
        combined_backtest['symbol'] = combined_backtest['symbol_naked'].combine_first(combined_backtest['symbol_covered'])

        # Check for missing symbols and handle them
        missing_symbols = combined_backtest['symbol'].isnull().sum()
        if missing_symbols > 0:
            logging.warning(f"{missing_symbols} records have null 'symbol'. They will be excluded from update.")
            # Remove these records
            combined_backtest = combined_backtest.dropna(subset=['symbol'])

        # Merge with barchart_opinion_current where overall_action is 'BUY'
        barchart_opinion_buy = barchart_opinion[barchart_opinion['overall_action'] == 'BUY']
        combined_backtest = pd.merge(
            combined_backtest,
            barchart_opinion_buy[['symbol', 'overall_percentage']],
            on='symbol',
            how='left'
        )

        # Get the most recent csv_download_date in opinion_event
        if opinion_event.empty:
            logging.warning("opinion_event table is empty. Opinion scores will be set to 0.")
            opinion_event_recent = pd.DataFrame()
        else:
            most_recent_date = opinion_event['csv_download_date'].max()
            opinion_event_recent = opinion_event[opinion_event['csv_download_date'] == most_recent_date]

        if not opinion_event_recent.empty:
            # Calculate opinion scores
            # Preference order:
            # 1. opinion_percent == 1
            # 2. previous_percent == 1
            # 3. last_week_percent == 1
            # 4. last_month_percent == 1
            opinion_event_recent = opinion_event_recent.copy()  # To avoid SettingWithCopyWarning
            opinion_event_recent['opinion_score'] = (
                (opinion_event_recent['opinion_percent'] == 1).astype(int) * 4 +
                (opinion_event_recent['previous_percent'] == 1).astype(int) * 3 +
                (opinion_event_recent['last_week_percent'] == 1).astype(int) * 2 +
                (opinion_event_recent['last_month_percent'] == 1).astype(int) * 1
            )

            # Aggregate opinion scores by symbol
            opinion_scores = opinion_event_recent.groupby('symbol')['opinion_score'].sum().reset_index()
        else:
            opinion_scores = pd.DataFrame(columns=['symbol', 'opinion_score'])

        combined_backtest = pd.merge(
            combined_backtest,
            opinion_scores,
            on='symbol',
            how='left'
        )

        # Fill NaN values with 0
        combined_backtest['cumulative_profit'] = combined_backtest['cumulative_profit'].fillna(0)
        combined_backtest['overall_percentage'] = combined_backtest['overall_percentage'].fillna(0)
        combined_backtest['opinion_score'] = combined_backtest['opinion_score'].fillna(0)

        # Calculate ranking_score
        # Define weights for each component
        WEIGHT_CUMULATIVE_PROFIT = 0.5
        WEIGHT_OVERALL_PERCENTAGE = 0.3
        WEIGHT_OPINION_SCORE = 0.2

        # Normalize the components to a 0-1 scale
        def normalize(series):
            min_val = series.min()
            max_val = series.max()
            if pd.isnull(min_val) or pd.isnull(max_val):
                # Handle entirely NaN series
                return series.apply(lambda x: 0.5)  # Assign a neutral value
            if max_val - min_val == 0:
                return series.apply(lambda x: 0.5)  # Assign a neutral value if no variation
            return (series - min_val) / (max_val - min_val)

        combined_backtest['norm_cumulative_profit'] = normalize(combined_backtest['cumulative_profit'])
        combined_backtest['norm_overall_percentage'] = normalize(combined_backtest['overall_percentage'])
        combined_backtest['norm_opinion_score'] = normalize(combined_backtest['opinion_score'])

        # Calculate the weighted ranking score
        combined_backtest['ranking_score'] = (
            WEIGHT_CUMULATIVE_PROFIT * combined_backtest['norm_cumulative_profit'] +
            WEIGHT_OVERALL_PERCENTAGE * combined_backtest['norm_overall_percentage'] +
            WEIGHT_OPINION_SCORE * combined_backtest['norm_opinion_score']
        )

        # Create a unified 'strategy_type' column
        combined_backtest['strategy_type'] = combined_backtest['strategy_type_naked'].combine_first(combined_backtest['strategy_type_covered'])

        # Check if 'strategy_type' was successfully created
        if combined_backtest['strategy_type'].isnull().any():
            logging.error("Some records are missing 'strategy_type'. Cannot proceed with update.")
            raise Exception("Missing 'strategy_type' in some records.")

        # Clean up redundant columns
        combined_backtest = combined_backtest.drop(['strategy_type_naked', 'strategy_type_covered', 'symbol_naked', 'symbol_covered'], axis=1, errors='ignore')

        # Select necessary fields for updating
        ranking_df = combined_backtest[['screener_id', 'strategy_type', 'symbol', 'ranking_score']]

        # Remove duplicates by taking the maximum ranking_score per screener_id and strategy_type
        ranking_df = ranking_df.groupby(['screener_id', 'strategy_type']).agg({
            'ranking_score': 'max',
            'symbol': 'first'  # Assuming symbol is consistent for each screener_id and strategy_type
        }).reset_index()

        # Sort screener_ids by ranking_score descending
        ranking_df = ranking_df.sort_values(by='ranking_score', ascending=False).reset_index(drop=True)

        # Assign ranking ranks (optional)
        ranking_df['rank'] = ranking_df.index + 1

        # Final ranking with screener_id, strategy_type, symbol, and ranking_score
        final_ranking = ranking_df[['screener_id', 'strategy_type', 'symbol', 'ranking_score']]

        # Update the advanced_screener table with the new ranking_score
        logging.info("Updating advanced_screener table with new ranking scores and updated_at timestamps...")

        # Batch update to improve performance
        # Supabase-py doesn't support bulk updates with different values in a single query,
        # so we'll perform updates in batches.

        # Define batch size for updates to prevent overloading the API
        BATCH_SIZE = 100  # Adjust as needed

        # Split the final_ranking into batches
        batches = [final_ranking[i:i + BATCH_SIZE] for i in range(0, final_ranking.shape[0], BATCH_SIZE)]

        total_updated = 0

        for idx, batch in enumerate(batches, start=1):
            logging.info(f"Processing batch {idx} with {batch.shape[0]} records...")
            
            # Get the current UTC time for the entire batch
            current_time = datetime.utcnow().isoformat() + 'Z'  # ISO 8601 format with 'Z' to indicate UTC

            # Convert batch to a list of dictionaries for bulk update
            update_data = batch.to_dict(orient="records")

            # Perform a bulk update using Supabase's upsert (if supported)
            try:
                response = supabase.from_("advanced_screener") \
                    .upsert(update_data, on_conflict= 'screener_id, strategy_type') \
                    .execute()

                if hasattr(response, 'error') and response.error:
                    logging.error(f"Error in bulk update: {response.error}")
                else:
                    total_updated += len(update_data)
            except Exception as e:
                logging.error(f"Exception in bulk update: {e}")

        logging.info(f"Successfully updated {total_updated} ranking scores with updated_at timestamps.")
        logging.info("Ranking algorithm completed successfully.")

    except Exception as e:
        logging.error(f"Error in run_algorithm: {e}")
        raise e  # Propagate exception to be handled by the caller

if __name__ == "__main__":
    try:
        run_algorithm()
    except Exception as e:
        logging.error(f"Algorithm execution failed: {e}")
        sys.exit(1)