# industry.py

import requests
import yfinance as yf
import pandas as pd
import logging
import time

# ===========================
# Configuration and Setup
# ===========================

# Configure logging to display informational messages, warnings, and errors
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

# Alpha Vantage API key (replace with your actual API key)
ALPHA_VANTAGE_API_KEY = "6AJ6T2NY2QGODKHV"

# ===========================
# Mock Data Definition
# ===========================

# Predefined list of stock ticker symbols (Mock Data)
MOCK_TICKER_LIST = [
    'AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN',
    'FB', 'JNJ', 'V', 'PG', 'XOM',
    'NFLX', 'NVDA', 'DIS', 'ADBE', 'PYPL',
    'INTC', 'CSCO', 'ORCL', 'CRM', 'BA'
    # Add more mock tickers as needed
]

# ===========================
# Data Fetching Functions
# ===========================

def get_sector_industry_story(ticker):
    """
    Fetches the sector, industry, and business summary (story) of a stock based on its ticker symbol using yfinance.

    Parameters:
    ticker (str): The stock ticker symbol (e.g., 'AAPL' for Apple Inc.)

    Returns:
    dict: A dictionary containing 'Ticker', 'sector', 'industry', and 'story'.
          If data is unavailable, respective fields are set to 'N/A'.
    """
    ticker_upper = ticker.upper()
    # try:
    #     stock = yf.Ticker(ticker_upper)
    #     info = stock.info

    #     sector = info.get('sector', 'N/A')
    #     industry = info.get('industry', 'N/A')
    #     story = info.get('longBusinessSummary', 'N/A')

    #     # Handle missing sector or industry
    #     if not sector or not industry:
    #         logging.warning(f"Sector or Industry data missing for ticker '{ticker_upper}'.")
    #         sector = sector if sector else 'N/A'
    #         industry = industry if industry else 'N/A'
        
    #     # Handle missing story
    #     if not story:
    #         logging.warning(f"Business summary (story) missing for ticker '{ticker_upper}'.")
    #         story = 'N/A'

    try:
        url = f"https://www.alphavantage.co/query?function=OVERVIEW&symbol={ticker_upper}&apikey={ALPHA_VANTAGE_API_KEY}"
        response = requests.get(url)
        response.raise_for_status()  # Raise an exception if the request failed
        
        data = response.json()

        # Fetch relevant data fields, fall back to 'N/A' if missing
        sector = data.get('Sector', 'N/A').title()  # Convert to title case
        industry = data.get('Industry', 'N/A').title()  # Convert to title case
        story = data.get('Description', 'N/A')

        return {
            'Ticker': ticker_upper,
            'sector': sector,       # Changed to lowercase
            'industry': industry,   # Changed to lowercase
            'story': story          # Changed to lowercase
        }
    
    except Exception as e:
        logging.error(f"Error fetching data for ticker '{ticker_upper}': {e}")
        return {
            'Ticker': ticker_upper,
            'sector': 'N/A',
            'industry': 'N/A',
            'story': 'N/A'
        }

def fetch_multiple_tickers(ticker_list, delay=0.5):
    """
    Fetches sector, industry, and business summary information for a list of stock tickers using yfinance.

    Parameters:
    ticker_list (list): List of stock ticker symbols.
    delay (float): Delay in seconds between API calls to prevent rate limiting.

    Returns:
    pd.DataFrame: DataFrame containing 'Ticker', 'sector', 'industry', and 'story' for each ticker.
    """
    results = []
    total = len(ticker_list)
    for idx, ticker in enumerate(ticker_list, 1):
        logging.info(f"Fetching data for {ticker} ({idx}/{total})...")
        data = get_sector_industry_story(ticker)
        results.append(data)
        time.sleep(delay)  # Pause to respect rate limits

    df = pd.DataFrame(results)
    return df

# ===========================
# Function for Flask Integration
# ===========================

def get_industry_data(ticker_list=None):
    """
    Fetches sector, industry, and business summary data for a list of tickers.

    Parameters:
    ticker_list (list): List of stock ticker symbols. If None, uses the predefined MOCK_TICKER_LIST.

    Returns:
    list: A list of dictionaries containing 'Ticker', 'sector', 'industry', and 'story'.
    """
    if ticker_list is None:
        ticker_list = MOCK_TICKER_LIST
        logging.info(f"Using predefined list of tickers: {', '.join(ticker_list)}")
    elif not ticker_list:
        logging.error("No valid tickers provided.")
        return []

    # Fetch sector, industry, and story data
    sector_industry_story_df = fetch_multiple_tickers(ticker_list)

    # Log the results
    logging.info("\nSector, Industry, and Story Information:")
    for index, row in sector_industry_story_df.iterrows():
        # Fixed the typo 'tory' to 'story' and used correct key casing
        logging.info(f"Ticker: {row['Ticker']}, sector: {row['sector']}, industry: {row['industry']}, story: {row['story'][:60]}...")  # Truncate story for logging

    # Convert DataFrame to a list of dictionaries for JSON serialization
    data = sector_industry_story_df.to_dict(orient='records')
    return data

# ===========================
# Removed Interactive Main Function
# ===========================

# The interactive main function and future integration placeholder are removed,
# as they are not needed for the Flask integration.


# import yfinance as yf
# import pandas as pd
# import logging
# import time

# # ===========================
# # Configuration and Setup
# # ===========================

# # Configure logging to display informational messages, warnings, and errors
# # Logs will be displayed on the console. For more advanced logging (e.g., to a file),
# # you can add a FileHandler to the logger.
# logging.basicConfig(
#     level=logging.INFO,
#     format='%(asctime)s - %(levelname)s - %(message)s'
# )

# # ===========================
# # Mock Data Definition
# # ===========================

# # Predefined list of stock ticker symbols (Mock Data)
# MOCK_TICKER_LIST = [
#     'AAPL', 'MSFT', 'GOOGL', 'TSLA', 'AMZN',
#     'FB', 'JNJ', 'V', 'PG', 'XOM',
#     'NFLX', 'NVDA', 'DIS', 'ADBE', 'PYPL',
#     'INTC', 'CSCO', 'ORCL', 'CRM', 'BA'
#     # Add more mock tickers as needed
# ]

# # ===========================
# # Data Fetching Functions
# # ===========================

# def get_sector_industry(ticker):
#     """
#     Fetches the sector and industry of a stock based on its ticker symbol using yfinance.

#     Parameters:
#     ticker (str): The stock ticker symbol (e.g., 'AAPL' for Apple Inc.)

#     Returns:
#     dict: A dictionary containing 'Ticker', 'Sector', and 'Industry'.
#           If data is unavailable, 'Sector' and 'Industry' are set to 'N/A'.
#     """
#     ticker_upper = ticker.upper()
#     try:
#         stock = yf.Ticker(ticker_upper)
#         info = stock.info

#         sector = info.get('sector', 'N/A')
#         industry = info.get('industry', 'N/A')

#         if not sector or not industry:
#             logging.warning(f"Sector or Industry data missing for ticker '{ticker_upper}'.")
#             sector = sector if sector else 'N/A'
#             industry = industry if industry else 'N/A'

#         return {'Ticker': ticker_upper, 'Sector': sector, 'Industry': industry}
#     except Exception as e:
#         logging.error(f"Error fetching data for ticker '{ticker_upper}': {e}")
#         return {'Ticker': ticker_upper, 'Sector': 'N/A', 'Industry': 'N/A'}

# def fetch_multiple_tickers(ticker_list, delay=0.5):
#     """
#     Fetches sector and industry information for a list of stock tickers using yfinance.

#     Parameters:
#     ticker_list (list): List of stock ticker symbols.
#     delay (float): Delay in seconds between API calls to prevent rate limiting.

#     Returns:
#     pd.DataFrame: DataFrame containing 'Ticker', 'Sector', and 'Industry' for each ticker.
#     """
#     results = []
#     total = len(ticker_list)
#     for idx, ticker in enumerate(ticker_list, 1):
#         logging.info(f"Fetching data for {ticker} ({idx}/{total})...")
#         data = get_sector_industry(ticker)
#         results.append(data)
#         time.sleep(delay)  # Pause to respect rate limits

#     df = pd.DataFrame(results)
#     return df

# # ===========================
# # Main Function
# # ===========================

# def main():
#     print("=== Stock Sector and Industry Fetcher ===\n")
#     print("Select Input Method:")
#     print("1. Use a predefined list of tickers (Mock Data)")
#     print("2. Enter tickers manually")
#     choice = input("Enter your choice (1 or 2): ").strip()

#     if choice == '1':
#         ticker_list = MOCK_TICKER_LIST
#         logging.info(f"Using predefined list of tickers: {', '.join(ticker_list)}")
#     elif choice == '2':
#         ticker_input = input("Enter stock ticker symbols separated by commas (e.g., AAPL, MSFT, GOOGL): ")
#         ticker_list = [ticker.strip().upper() for ticker in ticker_input.split(",") if ticker.strip()]
#     else:
#         logging.error("Invalid choice. Exiting.")
#         return

#     if not ticker_list:
#         logging.error("No valid tickers provided. Exiting.")
#         return

#     # Fetch sector and industry data
#     sector_industry_df = fetch_multiple_tickers(ticker_list)

#     # Log the results
#     logging.info("\nSector and Industry Information:")
#     for index, row in sector_industry_df.iterrows():
#         logging.info(f"Ticker: {row['Ticker']}, Sector: {row['Sector']}, Industry: {row['Industry']}")

#     # For now, we are only logging the results. Future integrations can add saving functionalities.

# # ===========================
# # Future Integration Placeholder
# # ===========================

# # The following is a placeholder for future integration with Supabase.
# # Since we are using mock data for now, the code below is commented out to prevent indentation errors.

# # """
# # # Future Integration with Supabase

# # In the future, to fetch tickers from Supabase and save results to Supabase, implement the following:

# # 1. **Install Supabase Client:**

# #    ```bash
# #    pip install supabase
# #    ```

# # 2. **Configure Supabase Client:**

# #    ```python
# #    from supabase import create_client, Client

# #    # Replace these with your actual Supabase project URL and API key
# #    SUPABASE_URL = 'https://your-supabase-url.supabase.co'
# #    SUPABASE_KEY = 'your-supabase-api-key'

# #    supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)
# #    ```

# # 3. **Define the Supabase Fetch Function:**

# #    ```python
# #    def get_tickers_from_supabase():
# #        """
# #        Fetches a list of stock tickers from Supabase.

# #        Returns:
# #        list: List of ticker symbols.
# #        """
# #        try:
# #            response = supabase.table('stocks').select('ticker').execute()
# #            if response.error:
# #                logging.error(f"Error fetching tickers from Supabase: {response.error}")
# #                return []
            
# #            tickers = [record['ticker'].upper() for record in response.data]
# #            logging.info(f"Fetched tickers from Supabase: {', '.join(tickers)}")
# #            return tickers
# #        except Exception as e:
# #            logging.error(f"Exception occurred while fetching tickers from Supabase: {e}")
# #            return []
# #    ```

# # 4. **Define the Supabase Save Function:**

# #    ```python
# #    def save_to_supabase(df):
# #        """
# #        Saves the sector and industry data to Supabase.

# #        Parameters:
# #        df (pd.DataFrame): DataFrame containing 'Ticker', 'Sector', and 'Industry'.
# #        """
# #        for index, row in df.iterrows():
# #            data = {
# #                'ticker': row['Ticker'],
# #                'sector': row['Sector'],
# #                'industry': row['Industry']
# #            }
# #            try:
# #                response = supabase.table('stocks').upsert(data).execute()
# #                if response.error:
# #                    logging.error(f"Failed to save data for ticker '{row['Ticker']}': {response.error}")
# #                else:
# #                    logging.info(f"Successfully saved data for ticker '{row['Ticker']}'.")
# #            except Exception as e:
# #                logging.error(f"Exception occurred while saving data for ticker '{row['Ticker']}': {e}")
# #    ```

# # 5. **Modify the `main` Function:**

# #    Replace or supplement the current log functionality with a call to `save_to_supabase`.

# #    ```python
# #    def main():
# #        print("=== Stock Sector and Industry Fetcher (Supabase Integration) ===\n")
# #        print("Select Input Method:")
# #        print("1. Use a predefined list of tickers (Mock Data)")
# #        print("2. Enter tickers manually")
# #        print("3. Fetch tickers from Supabase")
# #        choice = input("Enter your choice (1, 2, or 3): ").strip()

# #        if choice == '1':
# #            ticker_list = MOCK_TICKER_LIST
# #            logging.info(f"Using predefined list of tickers: {', '.join(ticker_list)}")
# #        elif choice == '2':
# #            ticker_input = input("Enter stock ticker symbols separated by commas (e.g., AAPL, MSFT, GOOGL): ")
# #            ticker_list = [ticker.strip().upper() for ticker in ticker_input.split(",") if ticker.strip()]
# #        elif choice == '3':
# #            ticker_list = get_tickers_from_supabase()
# #            if not ticker_list:
# #                logging.error("No tickers fetched from Supabase. Exiting.")
# #                return
# #        else:
# #            logging.error("Invalid choice. Exiting.")
# #            return

# #        if not ticker_list:
# #            logging.error("No valid tickers provided. Exiting.")
# #            return

# #        # Fetch sector and industry data
# #        sector_industry_df = fetch_multiple_tickers(ticker_list)

# #        # Display results
# #        print("\nSector and Industry Information:")
# #        print(sector_industry_df.to_string(index=False))

# #        # Optionally, save to Supabase
# #        save_option = input("\nWould you like to save the results to Supabase? (y/n): ").strip().lower()
# #        if save_option == 'y':
# #            save_to_supabase(sector_industry_df)
# #        else:
# #            print("Results not saved to Supabase.")
# #    ```
# #    """

# if __name__ == "__main__":
#     main()
