# app.py

from flask import Flask, jsonify, request, send_from_directory
import pandas as pd
from supabase import create_client, Client
from polygon import RESTClient
import sys
from datetime import datetime, date, timedelta
import os
from dotenv import load_dotenv
from logging_config import setup_logging
from celery_config import CELERY_BEAT_SCHEDULE, make_celery

# from celery import Celery

# Import the get_industry_data function from industry.py
from industry import get_industry_data

# Import model and backtest modules
from ml import models
import backtest
import utils
import algorithm
from utils import get_historical_prices, get_active_symbols  # Add this import near other utils imports
from backtest.main import run_complete_analysis
from backtest import update_screener_with_backtest, populate_advanced_screener
from backtest.compare import compare_predicted_with_actual
from backtest.ml_performance import update_ml_performance
from algorithm import run_algorithm

# Load environment variables from .env file
load_dotenv()

# Initialize Flask App
app = Flask(__name__)
logger = setup_logging(__name__)

# ================================
# Configuration Variables
# ================================

# Initialize Celery App
app.config.update({
    'broker_url': os.getenv('CELERY_BROKER_URL', 'redis://localhost:6379/0'),
    'result_backend': os.getenv('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0'),
    'timezone': 'America/New_York',
    'beat_schedule': CELERY_BEAT_SCHEDULE
})
celery = make_celery(app)

# Supabase Configuration
SUPABASE_URL = os.getenv("SUPABASE_URL")
SUPABASE_KEY = os.getenv("SUPABASE_KEY")
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Polygon.io Configuration
POLYGON_API_KEY = os.getenv("POLYGON_API_KEY")
polygon_client = RESTClient(POLYGON_API_KEY)

# Assign polygon_client to supabase_client for utility functions
supabase.polygon_client = polygon_client

# Starting Capital (Adjust as needed)
STARTING_CAPITAL = 100000.00

# ================================
# Logging Configuration
# ================================
# moved to logging_config
# ================================
# Helper Functions
# ================================

# Note: fetch_screener_data has been moved to utils.py
# Similarly, other utility functions are in utils.py

# ================================
# ML Model Initialization
# ================================

models.load_models()


# # Configure Celery
# celery = Celery(
#     app.name,
#     broker=os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0"),
#     backend=os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/0")
# )


# ================================
# Celery Tasks
# ================================

# # Initialize Celery with Flask app context
# celery.conf.update(app.config)

# @celery.task
# def train_models_task():
    # """
    # Celery task to train ML models for all screener symbols.
    # """
    # logger.info("Celery task started: train_models_task")
    # response = backtest.train_models_for_screeners(supabase, utils.get_historical_prices)
    # logger.info("Celery task completed: train_models_task")
    # return response

# @celery.task
# def run_backtesting_task():
#     backtest.verify_and_log_columns(supabase)
#     backtest.run_backtest(supabase, utils.fetch_screener_data, utils.get_historical_prices)
#     backtest.update_screener_with_backtest(supabase)
#     backtest.populate_advanced_screener(supabase, get_industry_data)
#     return {"status": "Backtesting and screener update completed successfully."}

# @app.route('/api/train_models', methods=['POST'])
# @require_api_key
# def train_models_endpoint():
#     task = train_models_task.delay()
#     return jsonify({"task_id": task.id, "status": "Model training initiated."}), 202

# @app.route('/api/backtesting', methods=['GET'])
# @require_api_key
# def get_backtesting():
#     task = run_backtesting_task.delay()
#     return jsonify({"task_id": task.id, "status": "Backtesting initiated."}), 202


# ================================
# Backtesting Functions
# ================================

# Note: All backtesting functions are now in backtest.py

# ================================
# Flask REST Endpoints
# ================================

@app.route('/api/industry', methods=['GET'])
def get_industry_endpoint():
    """
    Endpoint to retrieve industry data.
    Optionally, accepts a 'tickers' query parameter as a comma-separated list.
    Example: /api/industry?tickers=AAPL,MSFT,GOOGL
    """
    try:
        tickers_param = request.args.get('tickers')
        if tickers_param:
            ticker_list = [ticker.strip().upper() for ticker in tickers_param.split(',') if ticker.strip()]
            logger.debug(f"Received tickers: {ticker_list}")
        else:
            ticker_list = None  # Use default in industry.py
            logger.debug("No tickers provided. Fetching default industry data.")

        result = get_industry_data(ticker_list)  # Replace with actual function
        logger.debug(f"Raw industry data received: {result}")

        # Validate and process the result
        processed_result = []
        for item in result:
            ticker = item.get('Ticker', 'N/A')
            sector = item.get('sector', 'N/A')
            industry = item.get('industry', 'N/A')
            story = item.get('story', 'N/A')

            processed_result.append({
                'Ticker': ticker,
                'sector': sector,
                'industry': industry,
                'story': story
            })

        logger.info(f"Processed industry data for {len(processed_result)} tickers.")
        return jsonify(processed_result), 200
    except Exception as e:
        logger.exception(f"Error in /api/industry: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/forward_test_compare', methods=['GET'])
def compare_forward_test():
    try:
        compare_predicted_with_actual()
        return jsonify({"success": "Updated successfully"}), 200
    except Exception as e:
        logger.exception(f"Error in /api/industry: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/ml_performance_compare', methods=['GET'])
def update_ml_perfo():
    try:
        update_ml_performance()
        return jsonify({"success": "Updated successfully"}), 200
    except Exception as e:
        logger.exception(f"Error in /api/ml_performance_compare: {e}")
        return jsonify({"error": str(e)}), 500


@app.route('/api/train_models', methods=['POST'])
def train_models_endpoint():
    """
    Endpoint to trigger training of ML models for all screener symbols.
    """
    try:
        logger.info("Received request to train models.")
        response = backtest.train_models_for_screeners(supabase, utils.get_historical_prices)
        return jsonify({"status": "Model training initiated.", "details": response}), 200
    except AttributeError as ae:
        logger.error(f"AttributeError in /api/train_models: {ae}")
        return jsonify({"error": str(ae)}), 500
    except Exception as e:
        logger.exception(f"Error in /api/train_models: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/train_poor_models', methods=['GET'])
def train_poor_models_endpoint():
    from ml.retrain_models import retrain_poor_models
    """
    Endpoint to trigger training of ML models for all screener symbols.
    """
    try:
        logger.info("Received request to train models.")
        response = retrain_poor_models(supabase)
        return jsonify({"status": "Model training initiated."}), 200
    except AttributeError as ae:
        logger.error(f"AttributeError in /api/train_models: {ae}")
        return jsonify({"error": str(ae)}), 500
    except Exception as e:
        logger.exception(f"Error in /api/train_models: {e}")
        return jsonify({"error": str(e)}), 500
    
    # """
    # Endpoint to trigger training of ML models for all screener symbols asynchronously using Celery.
    # """
    # try:
    #     logger.info("Received request to train models.")
    #     task = train_models_task.delay()
    #     logger.info(f"Celery task initiated: {task.id}")
    #     return jsonify({"task_id": task.id, "status": "Model training initiated."}), 202
    # except Exception as e:
    #     logger.exception(f"Error in /api/train_models: {e}")
    #     return jsonify({"error": str(e)}), 500

@app.route('/api/predict', methods=['POST'])
def predict_endpoint():
    """
    Endpoint to make a prediction for a given symbol.
    Expects JSON payload with 'symbol', 'MA10', 'MA50', 'RSI'.
    Example:
    {
        "symbol": "AAPL",
        "MA10": 150.25,
        "MA50": 145.50,
        "RSI": 60.5
    }
    """
    try:
        data = request.get_json()
        symbol = data.get('symbol', '').upper()
        MA10 = data.get('MA10')
        MA50 = data.get('MA50')
        RSI = data.get('RSI')

        if not symbol or MA10 is None or MA50 is None or RSI is None:
            return jsonify({"error": "Missing required fields: 'symbol', 'MA10', 'MA50', 'RSI'."}), 400

        prediction = models.make_prediction_with_features(symbol,  [MA10, MA50, RSI])
        if prediction is None:
            return jsonify({"error": f"No model available for symbol {symbol}."}), 404

        return jsonify({"symbol": symbol, "prediction": prediction}), 200
    except Exception as e:
        logger.error(f"Error in /api/predict: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/backtesting', methods=['GET'])
def get_backtesting():
    try:
        # Get records to analyze
        records = get_active_symbols(supabase)
        
        results = []
        for record in records:
            # Run both historical backtest and forward prediction
            result = run_complete_analysis(supabase, record) #symbol type (symbol, screener_id, type, exp_date, strike, premium, iv, otm_prob)
            results.append(result)
            
        # Update screener tables
        # moving from naked_put_backtest_results_current to nakedput coveredcall screener
        # update_screener_with_backtest(supabase)
        
        # Update advanced screener with both historical and forward-looking metrics
        # adding to advance screener
        # populate_advanced_screener(supabase) 
        
        # # Run ranking algorithm
        run_algorithm()
        return jsonify({
            "status": "Analysis completed successfully",
            "symbols_processed": len(results)
        }), 200
        
    except Exception as e:
        logger.error(f"Analysis failed: {str(e)}")
        return jsonify({"status": "Analysis failed", "error": str(e)}), 500

@app.route('/api/backtest_results/naked_put/<int:screener_id>', methods=['GET'])
def get_naked_put_backtest_results(screener_id):
    """
    Retrieve backtest results for a specific Naked Put screener ID.
    """
    try:
        response = supabase.table('naked_put_backtest_results').select('*').eq('screener_id', screener_id).order('date.asc').execute()
        backtest_data = pd.DataFrame(response.data).to_dict(orient='records')
        return jsonify(backtest_data), 200
    except Exception as e:
        logger.error(f"Error retrieving Naked Put backtest results for screener_id {screener_id}: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/backtest_results/covered_call/<int:screener_id>', methods=['GET'])
def get_covered_call_backtest_results(screener_id):
    """
    Retrieve backtest results for a specific Covered Call screener ID.
    """
    try:
        # response = supabase.table('covered_call_backtest_results').select('*').eq('screener_id', screener_id).order('date', ascending=True).execute()
        response = supabase.table('covered_call_backtest_results').select('*').eq('screener_id', screener_id).order('date.asc').execute()
        backtest_data = pd.DataFrame(response.data).to_dict(orient='records')
        return jsonify(backtest_data), 200
    except Exception as e:
        logger.error(f"Error retrieving Covered Call backtest results for screener_id {screener_id}: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/populate_screener', methods=['POST'])
def populate_screener_data():
    """
    Populate the screener tables with average_profit, total_profit, and win_rate.
    """
    try:
        logger.info("Starting to update screener tables.")
        backtest.update_screener_with_backtest(supabase)
        logger.info("Screener tables updated successfully.")
        return jsonify({"status": "Screener tables updated successfully."}), 200
    except Exception as e:
        logger.error(f"Error updating screener tables: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/populate_advanced_screener', methods=['POST'])
def populate_advanced_screener_data():
    """
    Populate the advanced_screener table.
    """
    try:
        logger.info("Starting to populate the advanced screener table.")
        # backtest.populate_advanced_screener(supabase, get_industry_data)
        populate_advanced_screener(supabase)
        logger.info("Advanced screener populated successfully.")
        return jsonify({"status": "Advanced screener populated successfully."}), 200
    except Exception as e:
        logger.error(f"Error populating advanced_screener: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/advanced_screener', methods=['GET'])
def get_advanced_screener():
    """
    Retrieve the advanced screener data, ranked by ranking_score in descending order.
    """
    try:
        response = supabase.table('advanced_screener').select('*').order('ranking_score.desc').execute()
        screener_data = pd.DataFrame(response.data).to_dict(orient='records')
        return jsonify(screener_data), 200
    except Exception as e:
        logger.error(f"Error retrieving advanced screener data: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/api/update-rankings', methods=['GET'])
def update_rankings():
    """
    Update advanced screener rankings without running full backtest.
    """
    try:
        logger.info("Starting to update screener tables.")
        backtest.update_screener_with_backtest(supabase)
        logger.info("Screener tables updated successfully.")

        logger.info("Populating advanced_screener with backtest results.")
        backtest.populate_advanced_screener(supabase)
        logger.info("Advanced_screener populated successfully.")

        # Call the ranking algorithm
        try:
            logger.info("Executing the ranking algorithm.")
            algorithm.run_algorithm()
            logger.info("Ranking algorithm executed successfully.")
            return jsonify({"status": "Rankings updated successfully."}), 200
        except Exception as e:
            logger.error(f"Error executing ranking algorithm: {e}")
            return jsonify({"status": "Ranking update failed.", "error": str(e)}), 500
            
    except Exception as e:
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        logger.error(f"Ranking update failed: {e} in {fname} at line {exc_tb.tb_lineno}")
        return jsonify({"status": "Ranking update failed.", "error": str(e)}), 500

# Root Endpoint to serve index.html
@app.route('/')
def home():
    try:
        return send_from_directory('static', 'index.html')
    except Exception as e:
        logger.error(f"Error serving index.html: {e}")
        return jsonify({"error": "index.html not found."}), 404

# ================================
# Run Flask App
# ================================

if __name__ == '__main__':
  app.run(host='0.0.0.0', debug=True)

