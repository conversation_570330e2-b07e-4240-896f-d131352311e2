import pandas as pd
import yfinance as yf
import datetime
import ta

# Define user watchlist of stocks
watchlist = ['AAPL', 'TSLA', 'MSFT']  # User's watchlist

# Define function to fetch upcoming option expiration dates
def get_next_expiration_dates(stock):
    # Fetch option expiration dates from yfinance
    ticker = yf.Ticker(stock)
    expiration_dates = ticker.options
    current_date = datetime.datetime.now().date()
    
    # Filter to get the next monthly expiration date
    next_expiration = None
    for date in expiration_dates:
        exp_date = datetime.datetime.strptime(date, '%Y-%m-%d').date()
        if exp_date > current_date:
            next_expiration = exp_date
            break
    return next_expiration

# Define function to generate OTM strike prices for puts and calls
def generate_otm_strike_prices(current_price, otm_percentage=0.05):
    otm_put_strike = current_price * (1 - otm_percentage)  # 5% below the current price
    otm_call_strike = current_price * (1 + otm_percentage)  # 5% above the current price
    return round(otm_put_strike, 2), round(otm_call_strike, 2)

# Function to fetch stock data, calculate technical indicators, and generate trade signals
def process_stock(stock):
    # Fetch historical stock data
    end_date = datetime.datetime.now().date()
    start_date = end_date - datetime.timedelta(days=365)  # 1 year of data
    df = yf.download(stock, start=start_date, end=end_date)

    # Calculate Technical Indicators
    df['MA50'] = df['Close'].rolling(window=50).mean()
    df['RSI'] = ta.momentum.RSIIndicator(close=df['Close'], window=14).rsi()
    macd_indicator = ta.trend.MACD(close=df['Close'])
    df['MACD'] = macd_indicator.macd()
    df['MACD_Signal'] = macd_indicator.macd_signal()
    
    # Remove any NaN values resulting from indicator calculations
    df.dropna(inplace=True)
    
    # Get the current price and the next expiration date
    current_price = df['Close'][-1]
    next_expiration = get_next_expiration_dates(stock)
    
    # Generate OTM strike prices
    otm_put_strike, otm_call_strike = generate_otm_strike_prices(current_price)
    
    # Generate Trade Signals for Cash Flow Strategy
    signal = None
    if df['Close'][-1] > df['MA50'][-1] and df['MACD'][-1] > df['MACD_Signal'][-1] and 40 <= df['RSI'][-1] <= 60:
        signal = 'Sell Naked Put'  # Bullish signal
    elif df['Close'][-1] < df['MA50'][-1] and df['MACD'][-1] < df['MACD_Signal'][-1] and df['RSI'][-1] > 70:
        signal = 'Sell Covered Call'  # Bearish signal

    # Return the result for the stock
    return {
        'Stock': stock,
        'Current Price': current_price,
        'OTM Put Strike': otm_put_strike,
        'OTM Call Strike': otm_call_strike,
        'Next Expiration': next_expiration,
        'Signal': signal
    }

# Process the watchlist
def process_watchlist(watchlist):
    results = []
    for stock in watchlist:
        result = process_stock(stock)
        results.append(result)
    return pd.DataFrame(results)

# Run the watchlist analysis
result_df = process_watchlist(watchlist)

# Display the trade signals and opportunities
print(result_df)
