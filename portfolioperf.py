# import pandas as pd
# import yfinance as yf
# import datetime
# import ta

# # Define the stock symbol and time period
# symbol = 'AAPL'  # Replace with your desired stock symbol
# start_date = '2022-01-01'
# end_date = '2023-10-01'

# # Fetch historical price data using yfinance
# df = yf.download(symbol, start=start_date, end=end_date)

# # Ensure no missing data
# df.dropna(inplace=True)

# # Calculate Technical Indicators
# df['MA50'] = df['Close'].rolling(window=50).mean()
# df['RSI'] = ta.momentum.RSIIndicator(close=df['Close'], window=14).rsi()
# macd_indicator = ta.trend.MACD(close=df['Close'])
# df['MACD'] = macd_indicator.macd()
# df['MACD_Signal'] = macd_indicator.macd_signal()
# df['MACD_Hist'] = macd_indicator.macd_diff()

# # Initialize Trade Signals
# df['Signal'] = 0

# # Generate Signals for Selling Naked Puts
# df.loc[
#     (df['Close'] > df['MA50']) &
#     (df['MACD'] > df['MACD_Signal']) &
#     (df['RSI'] >= 40) & (df['RSI'] <= 60),
#     'Signal'
# ] = 1  # Signal to Sell Put

# # Generate Signals for Writing Covered Calls
# df.loc[
#     (df['Close'] <= df['MA50']) &
#     (df['MACD'] < df['MACD_Signal']) &
#     (df['RSI'] > 70),
#     'Signal'
# ] = -1  # Signal to Sell Call

# # Remove any NaN values resulting from indicator calculations
# df.dropna(inplace=True)

# # Initialize capital and position tracking
# capital = 100000  # Starting capital
# positions = []
# portfolio = []

# # Backtesting logic
# for index, row in df.iterrows():
#     date = index
#     signal = row['Signal']
#     close_price = row['Close']

#     if signal == 1:
#         # Simulate selling a cash-secured put
#         strike_price = close_price * 0.95  # Example strike price at 5% discount
#         premium = 2.0  # Hypothetical premium per share
#         position = {
#             'Entry Date': date,
#             'Entry Price': close_price,
#             'Position Type': 'Naked Put',
#             'Strike Price': strike_price,
#             'Premium': premium,
#             'Status': 'Open'
#         }
#         positions.append(position)
#         capital += premium * 100  # Collect premium
#         print(f"Sold naked put at strike {strike_price:.2f} on {date.date()}, collected premium {premium}")
#     elif signal == -1:
#         # Simulate selling a covered call
#         if capital >= close_price * 100:
#             # Buy 100 shares to cover the call
#             capital -= close_price * 100
#             strike_price = close_price * 1.05  # Example strike price at 5% premium
#             premium = 2.0  # Hypothetical premium per share
#             position = {
#                 'Entry Date': date,
#                 'Entry Price': close_price,
#                 'Position Type': 'Covered Call',
#                 'Strike Price': strike_price,
#                 'Premium': premium,
#                 'Status': 'Open'
#             }
#             positions.append(position)
#             capital += premium * 100  # Collect premium
#             print(f"Sold covered call at strike {strike_price:.2f} on {date.date()}, collected premium {premium}")
#         else:
#             print(f"Not enough capital to buy shares for covered call on {date.date()}")

# # Simulate position closure after 30 days or at expiry
# for position in positions:
#     entry_date = position['Entry Date']
#     exit_date = entry_date + datetime.timedelta(days=30)

#     try:
#         # Get the exit price on the closest available exit date
#         exit_date = df.index.asof(exit_date)
#         exit_price = df.loc[exit_date, 'Close']
#     except KeyError:
#         print(f"No data available for {exit_date}. Skipping this exit.")
#         continue

#     # Calculate P&L
#     if position['Position Type'] == 'Naked Put':
#         if exit_price >= position['Strike Price']:
#             # Option expires worthless, keep premium
#             pnl = position['Premium'] * 100
#         else:
#             # Assigned the stock, calculate loss
#             pnl = (exit_price - position['Strike Price']) * 100 + position['Premium'] * 100
#     elif position['Position Type'] == 'Covered Call':
#         if exit_price <= position['Strike Price']:
#             # Option expires worthless, keep premium and shares
#             pnl = position['Premium'] * 100
#             capital += exit_price * 100  # Sell shares at market price
#         else:
#             # Stock called away, calculate profit
#             pnl = (position['Strike Price'] - position['Entry Price']) * 100 + position['Premium'] * 100
#             capital += position['Strike Price'] * 100  # Receive strike price for shares

#     position.update({
#         'Exit Date': exit_date,
#         'Exit Price': exit_price,
#         'PnL': pnl,
#         'Status': 'Closed'
#     })
#     portfolio.append(position)
#     print(f"Closed position on {exit_date.date()}, PnL: {pnl:.2f}")

# # Output final capital and portfolio
# print(f"\nFinal Capital: ${capital:.2f}")
# print("\nPortfolio Performance:")
# portfolio_df = pd.DataFrame(portfolio)
# print(portfolio_df[['Entry Date', 'Exit Date', 'Position Type', 'Entry Price', 'Exit Price', 'Strike Price', 'Premium', 'PnL', 'Status']])


import pandas as pd
import yfinance as yf
import datetime
import ta

# Define the stock symbol and time period
symbol = 'AAPL'  # Replace with your desired stock symbol
start_date = '2022-01-01'
end_date = '2023-10-01'

# Fetch historical price data using yfinance
df = yf.download(symbol, start=start_date, end=end_date)

# Ensure no missing data
df.dropna(inplace=True)

# Calculate Technical Indicators
df['MA50'] = df['Close'].rolling(window=50).mean()
df['RSI'] = ta.momentum.RSIIndicator(close=df['Close'], window=14).rsi()
macd_indicator = ta.trend.MACD(close=df['Close'])
df['MACD'] = macd_indicator.macd()
df['MACD_Signal'] = macd_indicator.macd_signal()
df['MACD_Hist'] = macd_indicator.macd_diff()

# Initialize Trade Signals
df['Signal'] = 0

# Generate Signals for Selling Naked Puts
df.loc[
    (df['Close'] > df['MA50']) &
    (df['MACD'] > df['MACD_Signal']) &
    (df['RSI'] >= 40) & (df['RSI'] <= 60),
    'Signal'
] = 1  # Signal to Sell Put

# Generate Signals for Writing Covered Calls
df.loc[
    (df['Close'] <= df['MA50']) &
    (df['MACD'] < df['MACD_Signal']) &
    (df['RSI'] > 70),
    'Signal'
] = -1  # Signal to Sell Call

# Remove any NaN values resulting from indicator calculations
df.dropna(inplace=True)

# Step 1: Identify last trading day of each month
df['Month'] = df.index.to_period('M')
last_trading_days = df.groupby('Month').tail(1)  # Get the last trading day for each month

# Initialize capital and position tracking
capital = 100000  # Starting capital
positions = []
portfolio = []

# Step 2: Iterate over the last trading day of each month for backtesting
for index, row in last_trading_days.iterrows():
    date = index
    signal = row['Signal']
    close_price = row['Close']

    if signal == 1:
        # Simulate selling a cash-secured put
        strike_price = close_price * 0.95  # Example strike price at 5% discount
        premium = 2.0  # Hypothetical premium per share
        position = {
            'Entry Date': date,
            'Entry Price': close_price,
            'Position Type': 'Naked Put',
            'Strike Price': strike_price,
            'Premium': premium,
            'Status': 'Open'
        }
        positions.append(position)
        capital += premium * 100  # Collect premium
        print(f"Sold naked put at strike {strike_price:.2f} on {date.date()}, collected premium {premium}")
    elif signal == -1:
        # Simulate selling a covered call
        if capital >= close_price * 100:
            # Buy 100 shares to cover the call
            capital -= close_price * 100
            strike_price = close_price * 1.05  # Example strike price at 5% premium
            premium = 2.0  # Hypothetical premium per share
            position = {
                'Entry Date': date,
                'Entry Price': close_price,
                'Position Type': 'Covered Call',
                'Strike Price': strike_price,
                'Premium': premium,
                'Status': 'Open'
            }
            positions.append(position)
            capital += premium * 100  # Collect premium
            print(f"Sold covered call at strike {strike_price:.2f} on {date.date()}, collected premium {premium}")
        else:
            print(f"Not enough capital to buy shares for covered call on {date.date()}")

# Step 3: Simulate position closure at the last trading day of the next month
for position in positions:
    entry_date = position['Entry Date']
    entry_month = entry_date.to_period('M')

    try:
        # Find the last trading day of the next month for exit
        exit_month = (entry_month + 1).to_timestamp()
        exit_date = df.index.asof(exit_month)
        exit_price = df.loc[exit_date, 'Close']
    except KeyError:
        print(f"No data available for {exit_month}. Skipping this exit.")
        continue

    # Calculate P&L
    if position['Position Type'] == 'Naked Put':
        if exit_price >= position['Strike Price']:
            # Option expires worthless, keep premium
            pnl = position['Premium'] * 100
        else:
            # Assigned the stock, calculate loss
            pnl = (exit_price - position['Strike Price']) * 100 + position['Premium'] * 100
    elif position['Position Type'] == 'Covered Call':
        if exit_price <= position['Strike Price']:
            # Option expires worthless, keep premium and shares
            pnl = position['Premium'] * 100
            capital += exit_price * 100  # Sell shares at market price
        else:
            # Stock called away, calculate profit
            pnl = (position['Strike Price'] - position['Entry Price']) * 100 + position['Premium'] * 100
            capital += position['Strike Price'] * 100  # Receive strike price for shares

    position.update({
        'Exit Date': exit_date,
        'Exit Price': exit_price,
        'PnL': pnl,
        'Status': 'Closed'
    })
    portfolio.append(position)
    print(f"Closed position on {exit_date.date()}, PnL: {pnl:.2f}")

# Output final capital and portfolio
print(f"\nFinal Capital: ${capital:.2f}")
print("\nPortfolio Performance:")
portfolio_df = pd.DataFrame(portfolio)
print(portfolio_df[['Entry Date', 'Exit Date', 'Position Type', 'Entry Price', 'Exit Price', 'Strike Price', 'Premium', 'PnL', 'Status']])
