from supabase import Client
from typing import List
import os
from ml.models import train_and_save_model
import utils

# Define the threshold for poor performance
ACCURACY_THRESHOLD = 0.6
ERROR_THRESHOLD = 0.4

def retrain_poor_models(supabase_client):
    response = supabase_client.table("ml_model_performance").select("*").or_(
        f"accuracy_rate.lt.{ACCURACY_THRESHOLD},mean_absolute_error.gt.{ERROR_THRESHOLD}"
    ).execute()

    models = response.data

    if not models:
        print("No poorly performing models found.")
        return

    print(f"Found {len(models)} poorly performing models. Retraining...")

    for model in models:
        symbol = model['symbol']
        print(f"Retraining model for {symbol}...")
        train_and_save_model(symbol, utils.get_historical_prices, supabase_client)
    print("Model training completed")
