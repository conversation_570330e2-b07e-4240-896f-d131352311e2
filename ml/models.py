import pandas as pd
import numpy as np
import os
import logging
import glob
from datetime import date, timedelta, datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# PyTorch imports for the LSTM model
import torch
import torch.nn as nn
import torch.optim as optim

from sklearn.metrics import classification_report, accuracy_score
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, mean_squared_error
from xgboost import XGBClassifier

import joblib

from utils import get_historical_prices, upsert_screened_tickers, categorize_prob

# ===========================
# Logging Configuration
# ===========================

# Configure logging for this module
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("models.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ===========================
# Global Variables
# ===========================

# Define the features used for the ML model
FEATURES = ['MA10', 'MA50', 'RSI']

# Dictionaries to hold loaded models, scalers, and top features
MODELS = {}
SCALERS = {}
TOP_FEATURES = {}

# ===========================
# Feature Engineering
# ===========================

def add_features(df):
    """
    Add technical indicators and additional features to the DataFrame.

    Parameters:
    - df (pd.DataFrame): DataFrame containing historical price data.

    Returns:
    - pd.DataFrame: DataFrame with added features.
    """
    try:
        # Moving Averages
        df['MA10'] = df['close'].rolling(window=10).mean()
        df['MA50'] = df['close'].rolling(window=50).mean()

        # Exponential Moving Averages
        df['EMA10'] = df['close'].ewm(span=10, adjust=False).mean()
        df['EMA50'] = df['close'].ewm(span=50, adjust=False).mean()

        # Relative Strength Index (RSI)
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        window_length = 14
        avg_gain = gain.rolling(window=window_length, min_periods=1).mean()
        avg_loss = loss.rolling(window=window_length, min_periods=1).mean()
        RS = avg_gain / avg_loss
        df['RSI'] = 100 - (100 / (1 + RS))

        # MACD
        ema12 = df['close'].ewm(span=12, adjust=False).mean()
        ema26 = df['close'].ewm(span=26, adjust=False).mean()
        df['MACD'] = ema12 - ema26
        df['Signal_Line'] = df['MACD'].ewm(span=9, adjust=False).mean()

        # Stochastic Oscillator
        low14 = df['low'].rolling(window=14).min()
        high14 = df['high'].rolling(window=14).max()
        df['%K'] = 100 * ((df['close'] - low14) / (high14 - low14))
        df['%D'] = df['%K'].rolling(window=3).mean()

        # Williams %R
        df['Williams_%R'] = -100 * ((high14 - df['close']) / (high14 - low14))

        # On-Balance Volume (OBV)
        df['OBV'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()

        # Average True Range (ATR)
        df['H-L'] = df['high'] - df['low']
        df['H-Cp'] = abs(df['high'] - df['close'].shift())
        df['L-Cp'] = abs(df['low'] - df['close'].shift())
        df['TR'] = df[['H-L', 'H-Cp', 'L-Cp']].max(axis=1)
        df['ATR'] = df['TR'].rolling(window=14).mean()
        df.drop(['H-L', 'H-Cp', 'L-Cp', 'TR'], axis=1, inplace=True)

        # Commodity Channel Index (CCI)
        TP = (df['high'] + df['low'] + df['close']) / 3
        df['CCI'] = (TP - TP.rolling(window=20).mean()) / (0.015 * TP.rolling(window=20).std())

        # Add cyclical encoding for day_of_week and month if date index is present
        if 'day_of_week' in df.columns and 'month' in df.columns:
            df['sin_day_of_week'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
            df['cos_day_of_week'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
            df['sin_month'] = np.sin(2 * np.pi * df['month'] / 12)
            df['cos_month'] = np.cos(2 * np.pi * df['month'] / 12)

            # Drop original day_of_week and month columns to avoid redundancy
            df.drop(columns=['day_of_week', 'month'], inplace=True)

        df.bfill(inplace=True)
        df.ffill(inplace=True)

        logger.debug("Added technical indicators and cyclical encodings.")
        return df

    except Exception as e:
        logger.error(f"Error adding features: {e}")
        return None

# ===========================
# Dataset Preparation
# ===========================

def prepare_dataset(symbol, get_historical_prices_func, look_ahead=5, sequence_length=10):
    """
    Fetch historical data, add features, and prepare the dataset for ML.

    Parameters:
    - symbol (str): The stock symbol (e.g., 'AAPL').
    - get_historical_prices_func (function): Function to fetch historical prices.
    - look_ahead (int): Number of days ahead to predict.
    - sequence_length (int): Length of the input sequences for the LSTM.

    Returns:
    - tuple: X_train_seq, X_test_seq, y_train_seq, y_test_seq, scaler, top_features
    - None: If preparation fails
    """
    try:
        end_date = date.today()
        start_date = end_date - timedelta(days=5*365)  # Last 5 years

        # Fetch historical prices
        hist_df = get_historical_prices_func(symbol, start_date, end_date)
        if hist_df is None or hist_df.empty or len(hist_df) < 200:
            logger.warning(f"No data available for {symbol}. Skipping.")
            return None

        # Add features
        hist_df = add_features(hist_df)
        if hist_df is None or hist_df.empty:
            logger.warning(f"Feature engineering failed for {symbol}.")
            return None

        # Create labels
        hist_df['Future_Price'] = hist_df['close'].shift(-look_ahead)
        hist_df['Target'] = (hist_df['Future_Price'] > hist_df['close']).astype(int)
        hist_df.dropna(inplace=True)

        # Features and Target
        non_feature_columns = ['open', 'high', 'low', 'close', 'volume', 'Future_Price', 'Target']
        feature_columns = [col for col in hist_df.columns if col not in non_feature_columns]
        data = hist_df[feature_columns]
        targets = hist_df['Target']


        # Split data into training and testing sets (keeping time order)
        train_size = int(len(data) * 0.8)
        X_train = data.iloc[:train_size]
        X_test = data.iloc[train_size:]
        y_train = targets.iloc[:train_size]
        y_test = targets.iloc[train_size:]

        # Handle small datasets
        if len(data) < sequence_length:
            logger.warning(f"Dataset for {symbol} is too small for the given sequence length. Reducing sequence length.")
            sequence_length = max(2, len(data) // 2)  # Use at least a length of 2

        # XGBoost for feature selection
        xgb_model = XGBClassifier(use_label_encoder=False, eval_metric='logloss')
        xgb_model.fit(X_train, y_train)
        importances = xgb_model.feature_importances_
        feature_importances = pd.Series(importances, index=feature_columns)
        N = min(10, len(feature_importances))  # Use fewer features if the dataset is small
        top_features = feature_importances.nlargest(N).index.tolist()

        logger.info(f"Selected top features for {symbol}: {top_features}")

        # Use only the selected features
        X_train = X_train[top_features]
        X_test = X_test[top_features]

        # Feature Scaling
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)

        
        # Ensure the lengths match
        min_train_length = min(len(X_train_scaled), len(y_train.values))
        min_test_length = min(len(X_test_scaled), len(y_test.values))

        # Create sequences
        X_train_seq, y_train_seq = create_sequences(X_train_scaled, y_train.values, 
                                                     sequence_length if sequence_length <= min_train_length else min_train_length)
        X_test_seq, y_test_seq = create_sequences(X_test_scaled, y_test.values, 
                                                   sequence_length if sequence_length <= min_test_length else min_test_length)

        if len(X_train_seq) == 0 or len(X_test_seq) == 0:
            logger.warning(f"Insufficient data to create sequences for {symbol}.")
            return None

        logger.debug(f"Prepared training set for {symbol}: {len(X_train_seq)} samples.")
        logger.debug(f"Prepared testing set for {symbol}: {len(X_test_seq)} samples.")

        return X_train_seq, X_test_seq, y_train_seq, y_test_seq, scaler, top_features

    except Exception as e:
        logger.error(f"Error preparing dataset for {symbol}: {e}")
        return None

def create_sequences(X, y, sequence_length):
    """
    Create sequences of data for training/testing models, ensuring
    we handle cases where the data is less than the specified sequence length.
    """
    # If the data length is less than sequence_length, return empty arrays
    if len(X) < sequence_length:
        logger.warning("Not enough data to create sequences. Data length is less than sequence_length.")
        return np.array([]), np.array([])

    sequences_X = []
    sequences_y = []
    
    # Create sequences for the available data
    for i in range(len(X) - sequence_length + 1):
        sequences_X.append(X[i:i + sequence_length])
        sequences_y.append(y[i + sequence_length - 1]) 
    
    return np.array(sequences_X), np.array(sequences_y)
# ===========================
# Model Training and Saving
# ===========================

class LSTMModel(nn.Module):
    def __init__(self, input_size, hidden_size=50, num_layers=2):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # Define the LSTM layer
        self.lstm = nn.LSTM(input_size, hidden_size, num_layers, batch_first=True)
        # Define the output layer
        self.fc = nn.Linear(hidden_size, 1)
        # Define the activation function
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # Initialize hidden state and cell state
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size)

        # Forward propagate LSTM
        out, _ = self.lstm(x, (h0, c0))
        # Get last time step output
        out = out[:, -1, :]
        out = self.fc(out)
        out = self.sigmoid(out)
        return out

def train_model(X_train, y_train, sequence_length, n_features):
    """
    Train an LSTM model using the provided features and target.

    Parameters:
    - X_train (np.ndarray): Training features with shape (samples, timesteps, features).
    - y_train (np.ndarray): Training target array.
    - sequence_length (int): Length of input sequences.
    - n_features (int): Number of features in the dataset.

    Returns:
    - model: Trained PyTorch model or None if training fails.
    """
    try:
        # Convert data to PyTorch tensors
        X_train_tensor = torch.tensor(X_train, dtype=torch.float32)
        y_train_tensor = torch.tensor(y_train, dtype=torch.float32).view(-1, 1)

        # Define the model
        model = LSTMModel(input_size=n_features, hidden_size=50, num_layers=2)

        # Define the loss function and optimizer
        criterion = nn.BCELoss()
        optimizer = optim.Adam(model.parameters(), lr=0.001)

        # Training parameters
        epochs = 50
        batch_size = 32

        # Early stopping parameters
        patience = 5
        best_loss = np.inf
        patience_counter = 0

        # Training loop
        for epoch in range(epochs):
            model.train()
            permutation = torch.randperm(X_train_tensor.size(0))
            epoch_loss = 0.0
            for i in range(0, X_train_tensor.size(0), batch_size):
                # Ensure that the batch size is not larger than the remaining samples
                batch_end = min(i + batch_size, X_train_tensor.size(0))
                indices = torch.arange(i, batch_end)
                batch_X, batch_y = X_train_tensor[indices], y_train_tensor[indices]

                optimizer.zero_grad()
                outputs = model(batch_X)
                loss = criterion(outputs, batch_y)
                loss.backward()
                optimizer.step()
                epoch_loss += loss.item()

            epoch_loss /= max(1, (X_train_tensor.size(0) // batch_size))  # Ensure no division by zero
            logger.info(f"Epoch {epoch+1}/{epochs}, Loss: {epoch_loss:.4f}")

            # Early stopping
            if epoch_loss < best_loss:
                best_loss = epoch_loss
                patience_counter = 0
                best_model_state = model.state_dict()
            else:
                patience_counter += 1
                if patience_counter >= patience:
                    logger.info("Early stopping")
                    break

        # # Load best model state
        # model.load_state_dict(best_model_state)

        logger.info("Model training completed successfully.")
        return model
    except Exception as e:
        logger.error(f"Error training model: {e}")
        return None

def save_model(model, symbol):
    """
    Save the trained model to disk.

    Parameters:
    - model: Trained PyTorch model.
    - symbol (str): Stock symbol to name the model file.
    """
    try:
        # Define the directory to save models
        models_dir = 'models'
        os.makedirs(models_dir, exist_ok=True)

        # Define the model filename
        model_filename = os.path.join(models_dir, f"{symbol}_model.pth")

        # Save the model state_dict
        torch.save(model.state_dict(), model_filename)
        logger.info(f"Model saved to {model_filename}")
    except Exception as e:
        logger.error(f"Error saving model for {symbol}: {e}")

def log_model_training(supabase, symbol, model_type):
    # Check for existing versions for this symbol and model_type
    response = supabase.table("model_version_history") \
        .select("model_version") \
        .eq("symbol", symbol) \
        .eq("model_type", model_type) \
        .order("created_at", desc=True) \
        .limit(1) \
        .execute()

    if response.data == 200 and response.data:
        latest_version = int(response.data[0]['model_version']) + 1
    else:
        latest_version = 0

    # Insert new version record
    insert_response = supabase.table("model_version_history").insert({
        # "id": str(uuid.uuid4()),
        "symbol": symbol,
        "model_version": str(latest_version),
        "created_at": datetime.utcnow().isoformat(),
        "model_type": model_type,
    }).execute()

    if insert_response.data:
        print(f"Model version {latest_version} logged successfully.")
    else:
        print(f"Failed to log model version: {insert_response.json()}")

def train_and_save_model(symbol, get_historical_prices_func, supabase_client):
    """
    Train the ML model for a given symbol and save the model, scaler, and top features.
    """
    dataset = prepare_dataset(symbol, get_historical_prices_func)
    if dataset is None:
        logger.warning(f"Dataset preparation failed for symbol {symbol}.")
        return False

    X_train, X_test, y_train, y_test, scaler, top_features = dataset

    # Log the scaler for debugging
    logger.debug(f"Scaler after preparation for {symbol}: {scaler}")

    # Get sequence length and number of features
    sequence_length = X_train.shape[1]
    n_features = X_train.shape[2]

    # Initialize and train the model
    model = train_model(X_train, y_train, sequence_length, n_features)
    if model is None:
        logger.error(f"Model training failed for {symbol}.")
        return False

    # Evaluate the model
    X_test_tensor = torch.tensor(X_test, dtype=torch.float32)
    y_test_tensor = torch.tensor(y_test, dtype=torch.float32).view(-1, 1)

    model.eval()
    with torch.no_grad():
        y_pred_probs = model(X_test_tensor)
        y_pred_probs = y_pred_probs.detach().numpy().flatten()
        y_pred = (y_pred_probs > 0.5).astype(int)
    accuracy = accuracy_score(y_test, y_pred)
    logger.info(f"Model trained for {symbol} with accuracy: {accuracy:.2f}")
    logger.debug(f"Classification Report for {symbol}:\n{classification_report(y_test, y_pred, zero_division=0)}")

    save_success = upsert_screened_tickers(supabase_client, symbol, accuracy)
    if not save_success:
        logger.error(f"Failed to insert symbol {symbol} and accuracy to Supabase.")
    # Save the model
    save_model(model, symbol)

    # Save the scaler
    if scaler is None:
        logger.error(f"Scaler is not available for {symbol}.")
        return False

    # Ensure the directory exists
    models_dir = 'models'
    os.makedirs(models_dir, exist_ok=True)

    # Save the scaler
    try:
        scaler_filename = os.path.join(models_dir, f"{symbol}_scaler.joblib")
        joblib.dump(scaler, scaler_filename)
        logger.info(f"Scaler saved to {scaler_filename}")
    except Exception as e:
        logger.error(f"Error saving scaler for {symbol}: {e}")
        return False

    # Save the top features
    try:
        features_filename = os.path.join(models_dir, f"{symbol}_features.joblib")
        joblib.dump(top_features, features_filename)
        logger.info(f"Top features saved to {features_filename}")
    except Exception as e:
        logger.error(f"Error saving top features for {symbol}: {e}")
        return False
    
    log_model_training(supabase_client, symbol, "LSTM")
    return True  # Indicate success at the end

# ===========================
# Bulk Model Training
# ===========================

def train_models_for_screeners(supabase_client, get_historical_prices_func):
    """
    Train ML models for all symbols in the screener tables using pagination.
    """
    try:
        # Function to fetch symbols from a given table in batches of 1000
        def fetch_symbols_in_batches(table_name):
            limit = 1000
            offset = 0
            all_symbols = []
            
            while True:
                response = supabase_client.table(table_name) \
                                          .select('symbol') \
                                          .range(offset, offset+limit-1) \
                                          .execute()

                data = response.data

                # Debug logging for pagination
                if data and len(data) > 0:
                    logger.info(f"Fetched {len(data)} records from {table_name} starting at offset {offset}")
                    # Show a small sample of fetched symbols for debugging
                    sample_symbols = [record['symbol'].upper() for record in data[:10]]
                    logger.debug(f"Sample symbols from this batch: {sample_symbols}")
                else:
                    logger.info(f"No more records fetched from {table_name} at offset {offset}")

                if not data or len(data) == 0:
                    break

                # Extend our list with the new batch of symbols
                batch_symbols = [record['symbol'].upper() for record in data]
                all_symbols.extend(batch_symbols)
                offset += limit

            return all_symbols

        # Fetch symbols from each table
        naked_put_symbols = set(fetch_symbols_in_batches('naked_put_screener_current'))
        covered_call_symbols = set(fetch_symbols_in_batches('covered_call_screener_current'))

        # Log the counts obtained from pagination
        logger.info(f"Found {len(naked_put_symbols)} unique symbols in naked_put_screener_current after pagination.")
        logger.info(f"Found {len(covered_call_symbols)} unique symbols in covered_call_screener_current after pagination.")

        # Combine symbols and log
        all_symbols = naked_put_symbols.union(covered_call_symbols)
        logger.info(f"Found {len(all_symbols)} unique symbols to train models for.")

        # **Test without pagination**:
        # Fetch all records from one of the tables without any .range() calls to confirm total record count
        no_pagination_response = supabase_client.table('naked_put_screener_current').select('symbol').execute()
        logger.info(f"Test (no pagination) naked_put_screener_current returned {len(no_pagination_response.data)} records.")

        # If the no-pagination fetch also returns fewer records than expected,
        # check database counts directly in the Supabase dashboard or via direct SQL.

        # Proceed with the rest of your training logic (unchanged)
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_symbol = {
                executor.submit(train_and_save_model, symbol, get_historical_prices_func, supabase_client): symbol
                for symbol in all_symbols
            }

            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    success = future.result()
                    if success:
                        logger.info(f"Model training and saving successful for {symbol}.")
                    else:
                        logger.warning(f"Model training failed for {symbol}.")
                except Exception as e:
                    logger.error(f"Exception occurred while training model for {symbol}: {e}")

        logger.info("Completed training models for all screener symbols.")
    except Exception as e:
        logger.error(f"Error during bulk model training: {e}")

# ===========================
# Model Loading and Prediction
# ===========================

def load_models():
    """
    Load all trained models, scalers, and top features from the 'models/' directory.
    """
    models_dir = 'models'
    model_files = glob.glob(os.path.join(models_dir, '*_model.pth'))
    for model_file in model_files:
        symbol = os.path.basename(model_file).split('_model.pth')[0]
        scaler_file = os.path.join(models_dir, f"{symbol}_scaler.joblib")
        features_file = os.path.join(models_dir, f"{symbol}_features.joblib")
        if os.path.exists(scaler_file) and os.path.exists(features_file):
            # Load top features
            top_features = joblib.load(features_file)
            # Initialize model instance
            model = LSTMModel(input_size=len(top_features), hidden_size=50, num_layers=2)
            # Load model state
            model.load_state_dict(torch.load(model_file))
            model.eval()
            scaler = joblib.load(scaler_file)
            MODELS[symbol] = model
            SCALERS[symbol] = scaler
            TOP_FEATURES[symbol] = top_features
            logger.info(f"Loaded model, scaler, and features for {symbol} from '{models_dir}/'.")
        else:
            logger.warning(f"Scaler or features file not found for {symbol} in '{models_dir}/'. Skipping model loading.")

def load_model_for_symbol(symbol):
    """
    Load the trained model, scaler, and top features for a specific symbol from the 'models/' directory.

    Args:
        symbol (str): The symbol for which to load the model, scaler, and top features.

    Returns:
        tuple: A tuple containing the model, scaler, and top features if all files are found.
               Returns (None, None, None) if any file is missing.
    """

    models_dir = 'models'
    model_file = os.path.join(models_dir, f"{symbol}_model.pth")
    scaler_file = os.path.join(models_dir, f"{symbol}_scaler.joblib")
    features_file = os.path.join(models_dir, f"{symbol}_features.joblib")

    if os.path.exists(model_file) and os.path.exists(scaler_file) and os.path.exists(features_file):
        try:
            # Load top features
            top_features = joblib.load(features_file)

            # Initialize model instance
            model = LSTMModel(input_size=len(top_features), hidden_size=50, num_layers=2)

            # Load model state
            model.load_state_dict(torch.load(model_file))
            model.eval()

            # Load scaler
            scaler = joblib.load(scaler_file)

            # Store in global dictionaries
            MODELS[symbol] = model
            SCALERS[symbol] = scaler
            TOP_FEATURES[symbol] = top_features

            logger.info(f"Loaded model, scaler, and features for {symbol} from '{models_dir}/'.")

            return model, scaler, top_features
        except Exception as e:
            logger.error(f"Error loading files for {symbol}: {e}")
            return None, None, None
    else:
        logger.warning(f"Model, scaler, or features file not found for {symbol} in '{models_dir}/'.")
        return None, None, None

def make_prediction_with_features(symbol, features):
    """
    Make a prediction for a given symbol using the provided features.

    Parameters:
    - symbol (str): Stock ticker symbol.
    - features (list): List of feature values.

    Returns:
    - Prediction (1 for Buy, 0 for Sell) or None if model not found.
    """
    symbol = symbol.upper()
    if symbol not in MODELS or symbol not in SCALERS or symbol not in TOP_FEATURES:
        logger.warning(f"Model, scaler, or features not found for {symbol}.")
        return None

    model = MODELS[symbol]
    scaler = SCALERS[symbol]
    top_features = TOP_FEATURES[symbol]

    try:
        # Ensure the features are in the same order as top_features
        features_dict = dict(zip(top_features, features))
        ordered_features = [features_dict.get(feature, 0) for feature in top_features]

        # Validate feature completeness
        if None in ordered_features:
            missing_features = [f for f, val in zip(top_features, ordered_features) if val is None]
            logger.error(f"Missing feature values for {symbol}: {missing_features}")
            return None

        # Scale features
        features_scaled = scaler.transform([ordered_features])

        # Reshape for LSTM input
        features_scaled = features_scaled.reshape(1, 1, len(top_features))

        # Convert to tensor
        features_tensor = torch.tensor(features_scaled, dtype=torch.float32)

        # Make prediction
        model.eval()
        with torch.no_grad():
            prediction_prob = model(features_tensor).item()
            prediction = int(prediction_prob > 0.5)

        logger.info(f"Prediction for {symbol}: {categorize_prob(prediction_prob)} (Probability: {prediction_prob})")
        return prediction, prediction_prob

    except Exception as e:
        logger.error(f"Error making prediction for {symbol}: {e}")
        return None

def make_prediction(symbol, get_historical_prices_func, sequence_length=10):
    """
    Make a prediction for a given symbol using the loaded model.

    Parameters:
    - symbol (str): Stock ticker symbol.
    - get_historical_prices_func (function): Function to fetch historical prices.
    - sequence_length (int): Length of the input sequences.

    Returns:
    - Prediction (1 for Buy, 0 for Sell) or None if model not found.
    """
    symbol = symbol.upper()
    if symbol not in MODELS or symbol not in SCALERS or symbol not in TOP_FEATURES:
        logger.warning(f"Model, scaler, or features not found for {symbol}.")
        return None

    model = MODELS[symbol]
    scaler = SCALERS[symbol]
    top_features = TOP_FEATURES[symbol]

    # Fetch latest features
    features = get_latest_features(symbol, get_historical_prices_func, sequence_length, top_features)
    if features is None:
        logger.error(f"Could not get latest features for {symbol}.")
        return None

    try:
        # Scale features
        features_scaled = scaler.transform(features)

        # Reshape for LSTM input
        features_scaled = features_scaled.reshape(1, sequence_length, len(top_features))

        # Convert to tensor
        features_tensor = torch.tensor(features_scaled, dtype=torch.float32)

        # Make prediction
        model.eval()
        with torch.no_grad():
            prediction_prob = model(features_tensor).item()
            prediction = int(prediction_prob > 0.5)
        return prediction, prediction_prob
    except Exception as e:
        logger.error(f"Error making prediction for {symbol}: {e}")
        return None

def get_latest_features(symbol, get_historical_prices_func, sequence_length, top_features):
    """
    Fetch the latest features for the given symbol.

    Parameters:
    - symbol (str): Stock ticker symbol.
    - get_historical_prices_func (function): Function to fetch historical prices.
    - sequence_length (int): Length of the input sequences.
    - top_features (list): List of top features to select.

    Returns:
    - np.ndarray: Array of latest features.
    """
    try:
        end_date = date.today()
        start_date = end_date - timedelta(days=365)  # Last year
        hist_df = get_historical_prices_func(symbol, start_date, end_date)
        if hist_df is None or hist_df.empty:
            logger.warning(f"No historical data available for {symbol}.")
            return None
        hist_df = add_features(hist_df)
        if hist_df.empty:
            logger.warning(f"No features available after processing for {symbol}.")
            return None

        # Take the last `sequence_length` entries
        latest_data = hist_df.tail(sequence_length)
        if len(latest_data) < sequence_length:
            logger.warning(f"Not enough data to create a sequence for {symbol}. Required: {sequence_length}, Available: {len(latest_data)}")
            return None

        features = latest_data[top_features].values
        return features
    except Exception as e:
        logger.error(f"Error fetching latest features for {symbol}: {e}")
        return None
