# backtest.py

import pandas as pd
from datetime import datetime, date, timedelta
from logging_config import setup_logging

from supabase import create_client, Client  # Import the Supabase Client
import models
from utils import (
    is_valid_symbol,
    get_historical_prices,
    insert_backtest_result,
    fetch_screener_data,
    calculate_rsi,
    categorize_prob
)
# from industry import get_industry_data
from models import make_prediction, make_prediction_with_features, get_latest_features, train_and_save_model, TOP_FEATURES, load_models, load_model_for_symbol
import algorithm  # Import the run_algorithm function
from concurrent.futures import ThreadPoolExecutor, as_completed
import traceback


import os
import sys
from decimal import Decimal
from backtest.position import Position
from backtest.historical import run_historical_backtest
from backtest.forward_test import run_forward_test

# Load models to populate TOP_FEATURES
load_models()
logger = setup_logging(__name__)

def short_put_intrinsic_value(strike, current_price):
    """Calculate intrinsic value for a short put position"""
    return max(0, (strike - current_price) * 100)

def short_call_intrinsic_value(strike, current_price):
    """Calculate intrinsic value for a short call position"""
    return max(0, (current_price - strike) * 100)

def train_models_for_screeners(supabase_client, get_historical_prices_func):
    """
    Train ML models for all symbols in the screener tables using pagination.
    """
    try:
        # Function to fetch symbols from a given table in batches of 1000
        def fetch_symbols_in_batches(table_name):
            limit = 1000
            offset = 0
            all_symbols = []
            
            while True:
                response = supabase_client.table(table_name) \
                                          .select('symbol') \
                                          .range(offset, offset+limit-1) \
                                          .execute()

                data = response.data

                # Debug logging for pagination
                if data and len(data) > 0:
                    logger.info(f"Fetched {len(data)} records from {table_name} starting at offset {offset}")
                    # Show a small sample of fetched symbols for debugging
                    sample_symbols = [record['symbol'].upper() for record in data[:10]]
                    logger.debug(f"Sample symbols from this batch: {sample_symbols}")
                else:
                    logger.info(f"No more records fetched from {table_name} at offset {offset}")
                    break

                # Extend our list with the new batch of symbols
                batch_symbols = [record['symbol'].upper() for record in data]
                all_symbols.extend(batch_symbols)
                offset += limit

            return all_symbols

        # Fetch symbols from each table
        naked_put_symbols = set(fetch_symbols_in_batches('naked_put_screener_current'))
        covered_call_symbols = set(fetch_symbols_in_batches('covered_call_screener_current'))

        # Log the counts obtained from pagination
        logger.info(f"Found {len(naked_put_symbols)} unique symbols in naked_put_screener_current after pagination.")
        logger.info(f"Found {len(covered_call_symbols)} unique symbols in covered_call_screener_current after pagination.")

        # Combine symbols and log
        all_symbols = naked_put_symbols.union(covered_call_symbols)
        logger.info(f"Found {len(all_symbols)} unique symbols to train models for.")

        # **Test without pagination**:
        # Fetch all records from one of the tables without any .range() calls to confirm total record count
        no_pagination_response = supabase_client.table('naked_put_screener_current').select('symbol').execute()
        logger.info(f"Test (no pagination) naked_put_screener_current returned {len(no_pagination_response.data)} records.")

        # Proceed with the rest of your training logic (unchanged)
        with ThreadPoolExecutor(max_workers=5) as executor:
            future_to_symbol = {
                executor.submit(train_and_save_model, symbol, get_historical_prices_func, supabase_client): symbol
                for symbol in all_symbols
            }

            for future in as_completed(future_to_symbol):
                symbol = future_to_symbol[future]
                try:
                    success = future.result()
                    if success:
                        logger.info(f"Model training and saving successful for {symbol}.")
                    else:
                        logger.warning(f"Model training failed for {symbol}.")
                except Exception as e:
                    logger.error(f"Exception occurred while training model for {symbol}: {e}")

        logger.info("Completed training models for all screener symbols.")
    except Exception as e:
        logger.error(f"Error during bulk model training: {e}")

def train_models_for_symbol(symbol: str, supabase_client: Client):
    """
    Train ML models for symbol with no model available.

    Parameters:
    symbol (str): Ticker symbol
    supabase_client (Client): Supabase client instance.

    Returns:
    dict: Status message indicating success or failure.
    """
    try:
        logger.info(f"Training and saving model for symbol: {symbol}")
        success = train_and_save_model(symbol, get_historical_prices ,supabase_client)
        if success:
            logger.info(f"Model training and saving successful for {symbol}.")
        else:
            logger.warning(f"Model training and saving failed for {symbol}.")

        return

    except Exception as e:
        logger.error(f"Error in train_models_for_screeners: {e}")
        return {"status": "Failed to train models.", "error": str(e)}

def calculate_PoP(supabase_client, table_name, screener_data, advanced_screener_entries, strategy_type):
    """
    Calculate Probability of Profit (PoP) for a given screener_id.
    """
    try:
        # Extracting the list of screener_id values from the list of objects
        screener_ids = [item['id'] for item in screener_data]
        response = (
                supabase_client.table(table_name)
                .select("daily_profit, screener_id")
                .in_("screener_id", screener_ids)
                .execute()
            )
        
        data = response.data
        pop =  0.0
        try:
            for record in screener_data:
                screener_id = record["id"]
                if not data:
                    pop =  0.0
                else:
                    # Ensure 'data' is a list (even if it is a single object)
                    if not isinstance(data, list):
                        data = [data]
                    daily_profit = [item for item in data if item['screener_id'] == screener_id]
                    if daily_profit:
                        df = pd.DataFrame(daily_profit)
                        total_trades = len(df)
                        profitable_trades = len(df[df["daily_profit"] > 0])
                        pop = round((profitable_trades / total_trades) * 100 if total_trades > 0 else 0.0, 2)
                    else:
                        pop = 0.0
                
                symbol = record["symbol"].upper()
                iv = float(record.get("iv", 0.0))
                    # Get sequence_length and top_features
                sequence_length = 10  # Or the value you used during training
                top_features = TOP_FEATURES.get(symbol)
                if not top_features:
                    logger.error(f"Top features not found for {symbol}. Setting prediction None.")

                features = get_latest_features(
                    symbol, get_historical_prices, sequence_length, top_features
                ) if top_features is not None else None

                prediction, prediction_prob = make_prediction(
                    symbol, get_historical_prices, sequence_length
                ) if features is not None else None, None
                ml_ranking = calculate_ml_ranking(
                    pop, iv, prediction if prediction is not None else 0
                )
                
                # Prepare data dictionary
                data = {
                    "screener_id": screener_id,
                    "symbol": symbol,
                    "strategy_type": strategy_type,
                    "probability_of_profit": pop,
                    "implied_volatility": iv,
                    "sector": 'N/A',
                    "industry": None,
                    "story": 'N/A',
                    "ml_prediction": prediction_prob if prediction_prob is not None else "N/A",
                    "ml_ranking": ml_ranking,
                    "ml_opinion": categorize_prob(prediction_prob) if prediction_prob is not None else "N/A",
                    "created_at": datetime.utcnow().isoformat(),
                }

                advanced_screener_entries.append(data)

            return advanced_screener_entries

        except Exception as e:
             # Get the detailed traceback including the line number
            error_message = f"Error calculating PoP for screener_id {screener_id} in {table_name}: {str(e)}"
            error_traceback = traceback.format_exc()  # This will give you the stack trace with line numbers
            logger.error(f"{error_message}\n{error_traceback}")
            return advanced_screener_entries

    except Exception as e:
        logger.error(
            f"Error calculating metrices in {table_name}: {e}"
        )
        return advanced_screener_entries


def calculate_average_IV(supabase_client, table_name, screener_id):
    """
    Calculate average Implied Volatility (IV) for a given screener_id.
    Assumes that the screener tables have an 'implied_volatility' field.
    """
    try:
        response = (
            supabase_client.table(table_name)
            .select("implied_volatility")
            .eq("id", screener_id)
            .execute()
        )
        data = response.data
        if not data or "implied_volatility" not in data[0]:
            return 0.0
        iv = float(data[0]["implied_volatility"])
        return round(iv, 2)
    except Exception as e:
        logger.error(
            f"Error fetching IV for screener_id {screener_id} from {table_name}: {e}"
        )
        return 0.0


def calculate_ml_ranking(pop, iv, ml_prediction=0):
    """
    Calculate a ranking score based on PoP, IV, and ML Prediction.
    Higher PoP, Lower IV, and Buy Prediction are better.
    """
    try:
        # Weight PoP as 60%, IV as 30%, ML Prediction as 10%
        pop_score = pop * 0.6
        iv_score = (100 - iv) * 0.3  # Assuming IV ranges between 0-100
        ml_score = 10 if ml_prediction == 1 else 0  # 10 points if Buy signal

        ml_ranking = pop_score + iv_score + ml_score
        return round(ml_ranking, 2)
    except Exception as e:
        logger.error(
            f"Error calculating ranking score with PoP {pop}, IV {iv}, ML Prediction {ml_prediction}: {e}"
        )
        return 0.0


# def get_last_backtest(supabase_client, table_name, screener_id):
#     """
#     Retrieve the latest backtest record for a given screener_id.
#     Returns the latest date, capital, cumulative_profit, and status.
#     If no records exist, returns None.
#     """
#     try:
#         response = (
#             supabase_client.table(table_name)
#             .select("*")
#             .eq("screener_id", screener_id)
#             .order("date.desc")
#             .limit(1)
#             .execute()
#         )
#         data = response.data
#         if not data:
#             return None
#         latest_record = data[0]
#         latest_date = datetime.strptime(latest_record["date"], "%Y-%m-%d").date()
#         capital = float(latest_record["capital"])
#         cumulative_profit = float(latest_record["cumulative_profit"])
#         status = latest_record["status"]
#         return latest_date, capital, cumulative_profit, status
#     except Exception as e:
#         logger.error(
#             f"Error fetching last backtest for screener_id {screener_id} in {table_name}: {e}"
#         )
#         return None

def get_last_backtest_batch(supabase_client, table_name, screener_ids):
    """
    Retrieve the latest backtest records for a batch of screener_ids.
    Returns a dictionary where each key is a screener_id and the value is a tuple 
    of the latest date, capital, cumulative_profit, and status.
    If no record exists for a screener_id, the value will be None.
    """
    results = {}
    try:
        # Fetch backtest data for all screener_ids in a single query
        response = (
            supabase_client.table(table_name)
            .select("*")
            .in_("screener_id", screener_ids)  # Using `in_` to fetch data for multiple screener_ids
            .order("date.desc")
            .execute()
        )
        data = response.data

        # Organize the data into a dictionary
        for record in data:
            screener_id = record["screener_id"]
            latest_date = datetime.strptime(record["date"], "%Y-%m-%d").date()
            capital = float(record["capital"])
            cumulative_profit = float(record["cumulative_profit"])
            status = record["status"]
            results[screener_id] = (latest_date, capital, cumulative_profit, status)

        # Fill in None for any missing screener_id
        for screener_id in screener_ids:
            if screener_id not in results:
                results[screener_id] = None

        return results

    except Exception as e:
        logger.error(
            f"Error fetching last backtest records for screener_ids {screener_ids} in {table_name}: {e}"
        )
        return None


# TODO: check the logic in strategy / function, premium, expiry etc
def backtest_naked_put(
    supabase_client,
    record,
    hist_df,
    start_date,
    initial_capital,
    initial_cumulative_profit,
    initial_status
):
    try:
        # Create position object
        position = Position(
            symbol=record['symbol'],
            strike=Decimal(str(record['strike'])),
            premium=Decimal(str(record['bid'])),
            expiration=datetime.strptime(record['expiration_date'], '%Y-%m-%d').date(),
            position_type='naked_put'
        )
        
        # Run historical backtest
        results_df = run_historical_backtest(position, hist_df, start_date)
        
        # Run forward test for ML predictions
        forward_results = run_forward_test(record['symbol'], hist_df)
        
        # Store results
        for _, row in results_df.iterrows():
            insert_backtest_result(
                supabase_client=supabase_client,
                table_name="naked_put_backtest_results",
                screener_id=record["id"],
                date_=row['date'],
                daily_profit=row['daily_pnl'],
                cumulative_profit=row['total_pnl'],
                capital=initial_capital + row['total_pnl'],
                status=initial_status,
                prob=forward_results['prediction_probability'] if forward_results else 0.0,
                ma10=row['ma10'],
                ma50=row['ma50'],
                rsi=row['rsi']
            )
            
    except Exception as e:
        logger.error(f"Backtest failed for {record['symbol']}: {str(e)}")
        raise

def insert_backtest_results_batch(supabase_client, result_data):
    """Insert batch of backtest results into the database."""
    if not result_data:
        return
    try:
        supabase_client.table('naked_put_backtest_results').insert(result_data).execute()
        logger.info(f"Inserted {len(result_data)} backtest results into database.")
    except Exception as e:
        logger.error(f"Error inserting backtest results in batch: {e}")


def backtest_covered_call(
    supabase_client,
    record,
    hist_df,
    start_date,
    initial_capital,
    initial_cumulative_profit,
    initial_status
):
    try:
        symbol = record["symbol"]
        # Load the ML model for predictions, but handle failure gracefully
        try:
            # train_models_for_symbol(symbol, supabase_client)
            load_model_for_symbol(symbol)
            ml_available = True
        except Exception as e:
            logger.warning(f"ML model loading failed for {symbol}: {str(e)}")
            ml_available = False
        
        strike = float(record["strike"])
        premium = float(record["bid"])
        expiration = datetime.strptime(record["expiration_date"], "%Y-%m-%d").date()

        capital = initial_capital
        cumulative_profit = initial_cumulative_profit
        status = initial_status

        # Calculate technical indicators
        if "MA10" not in hist_df.columns:
            hist_df["MA10"] = hist_df["close"].rolling(window=10, min_periods=1).mean()
        if "MA50" not in hist_df.columns:
            hist_df["MA50"] = hist_df["close"].rolling(window=50, min_periods=1).mean()
        if "RSI" not in hist_df.columns:
            hist_df["RSI"] = calculate_rsi(hist_df["close"])

        start_ts = pd.to_datetime(start_date)
        filtered = hist_df[hist_df.index >= start_ts]
        
        if filtered.empty:
            return

        position_opened = False
        prev_unrealized_pnl = 0.0
        stock_entry_price = None

        for ts, row in filtered.iterrows():
            current_date = ts.date()
            daily_profit = 0.0
            current_price = float(row["close"])

            # Get ML prediction with error handling
            try:
                if ml_available:
                    features = [row["MA10"], row["MA50"], row["RSI"]]
                    prediction_result = make_prediction_with_features(symbol, features)
                    if prediction_result is not None:
                        prediction, prediction_prob = prediction_result
                    else:
                        prediction_prob = 0.0
                else:
                    prediction_prob = 0.0
            except Exception as e:
                logger.warning(f"Prediction failed for {symbol} on {current_date}: {str(e)}")
                prediction_prob = 0.0

            if status == "Active":
                if not position_opened:
                    # Buy 100 shares of stock and collect option premium
                    stock_entry_price = current_price
                    stock_cost = current_price * 100
                    capital -= stock_cost
                    daily_profit = premium * 100
                    position_opened = True
                    
                    # Initialize mark-to-market
                    stock_value = current_price * 100
                    current_intrinsic = short_call_intrinsic_value(strike, current_price)
                    current_unrealized = (stock_value - stock_cost) + (premium * 100) - current_intrinsic
                    prev_unrealized_pnl = current_unrealized
                else:
                    # Daily mark-to-market P&L
                    stock_value = current_price * 100
                    stock_cost = stock_entry_price * 100
                    current_intrinsic = short_call_intrinsic_value(strike, current_price)
                    current_unrealized = (stock_value - stock_cost) + (premium * 100) - current_intrinsic
                    daily_profit = current_unrealized - prev_unrealized_pnl
                    prev_unrealized_pnl = current_unrealized

                capital += daily_profit
                cumulative_profit += daily_profit

            # Insert daily result
            insert_backtest_result(
                supabase_client=supabase_client,
                table_name="covered_call_backtest_results",
                screener_id=record["id"],
                date_=current_date,
                daily_profit=daily_profit,
                cumulative_profit=cumulative_profit,
                capital=capital,
                status=status,
                prob=prediction_prob,
                ma10=row["MA10"],
                ma50=row["MA50"],
                rsi=row["RSI"]
            )

        # Handle expiration
        if status == "Active" and expiration <= date.today():
            status = "Expired"
            
            # Get final ML prediction with error handling
            try:
                if ml_available:
                    last_row = filtered.iloc[-1]
                    features = [last_row["MA10"], last_row["MA50"], last_row["RSI"]]
                    prediction_result = make_prediction_with_features(symbol, features)
                    if prediction_result is not None:
                        prediction, final_prediction_prob = prediction_result
                    else:
                        final_prediction_prob = 0.0
                else:
                    final_prediction_prob = 0.0
            except Exception as e:
                logger.warning(f"Final prediction failed for {symbol} on {expiration}: {str(e)}")
                final_prediction_prob = 0.0

            if current_price > strike:
                # Called away: Sell stock at strike price
                final_stock_profit = (strike - stock_entry_price) * 100
            else:
                # Keep stock, option expires worthless
                final_stock_profit = (current_price - stock_entry_price) * 100
            
            capital += final_stock_profit
            cumulative_profit += final_stock_profit
            
            insert_backtest_result(
                supabase_client=supabase_client,
                table_name="covered_call_backtest_results",
                screener_id=record["id"],
                date_=expiration,
                daily_profit=final_stock_profit,
                cumulative_profit=cumulative_profit,
                capital=capital,
                status=status,
                prob=final_prediction_prob,
                ma10=last_row["MA10"],
                ma50=last_row["MA50"],
                rsi=last_row["RSI"]
            )

    except Exception as e:
        logger.error(f"Error in backtest_covered_call: {str(e)}")
        raise


def verify_and_log_columns(supabase_client):
    """
    Verify if required columns exist in screener tables.
    Logs warnings if any columns are missing.
    """
    required_columns = {
        "naked_put_screener": ["average_profit", "total_profit", "win_rate"],
        "covered_call_screener": ["average_profit", "total_profit", "win_rate"],
    }
    try:
        for table, columns in required_columns.items():
            # Using Supabase's table description API if available, otherwise skipping
            # Here, we'll assume a function 'pg_table_def' exists or implement accordingly
            response = supabase_client.rpc("pg_table_def", {"table": table}).execute()
            table_def = pd.DataFrame(response.data)
            existing_columns = table_def["column_name"].tolist()
            for column in columns:
                if column not in existing_columns:
                    logger.warning(
                        f"Column '{column}' is missing in table '{table}'. Please add it to avoid errors."
                    )
    except Exception as e:
        logger.error(f"Error verifying columns: {e}")


# Function to chunk the DataFrame into batches of size 1000
def chunk_dataframe(df, chunk_size):
    """Yield successive chunks of the dataframe."""
    for i in range(0, len(df), chunk_size):
        yield df.iloc[i:i + chunk_size]



def run_backtest(supabase_client):
    """
    Run backtesting for all records in Naked Put and Covered Call screeners.
    Utilizes the fetch_screener_data function imported from utils.py.
    """
    # Define the batch size
    batch_size = 1000

    logger.info("Starting the backtesting process.")
    naked_put_data, covered_call_data = fetch_screener_data(
        supabase_client
    )  # Use the imported fetch_screener_data function

    # Backtest Naked Puts
    logger.info("Starting backtesting for Naked Puts.")
    # Loop over chunks of the DataFrame
    for batch in chunk_dataframe(naked_put_data, batch_size):
        # Get the list of screener_ids from the batch
        screener_ids = batch['id'].tolist()
        
        # Fetch the last backtest records in batch
        last_backtest_results = get_last_backtest_batch(supabase_client, "naked_put_backtest_results", screener_ids)
        hist_df_dict = {}
        for index, record in batch.iterrows():
            symbol = record["symbol"]
            screener_id = record["id"]

            # Validate symbol
            if not is_valid_symbol(symbol, supabase_client.polygon_client):
                logger.warning(f"Invalid or unsupported symbol: {symbol}. Skipping backtest for ID {screener_id}.")
                continue

            upload_date = datetime.strptime(record["upload_date"], "%Y-%m-%d").date()
            expiration = datetime.strptime(record["expiration_date"], "%Y-%m-%d").date()
            today = date.today()
            expiration_in_future = expiration > today

            # Retrieve the last backtest result for the screener_id in the batch
            last_backtest = last_backtest_results.get(screener_id)

            if last_backtest:
                last_date, capital, cumulative_profit, status = last_backtest
                start_date = last_date + timedelta(days=1)
                logger.info(f"Last backtest for Naked Put ID {screener_id}: Date={last_date}, Capital={capital}, Cumulative Profit={cumulative_profit}, Status={status}")
            else:
                start_date = upload_date
                capital = 100000.00  # STARTING_CAPITAL
                cumulative_profit = 0.0
                status = "Active"
                logger.info(f"No previous backtest found for Naked Put ID {screener_id}. Starting from upload date {upload_date}.")

            # Determine the end date
            end_date = min(expiration, today)
            if start_date > end_date:
                logger.info(f"No new data to backtest for Naked Put ID {screener_id} ({symbol}).")
                continue

            logger.info(f"Backtesting Naked Put ID {screener_id} for {symbol}: Start Date = {start_date}, End Date = {end_date}, Expiration in Future: {expiration_in_future}")

            hist_df = get_historical_prices(symbol, start_date, end_date)
            if hist_df is not None and not hist_df.empty:
                backtest_naked_put(
                    supabase_client,
                    record,
                    hist_df,
                    start_date,
                    capital,
                    cumulative_profit,
                    status,
                )
                logger.info(f"Backtested Naked Put ID {screener_id} ({symbol}) from {start_date} to {end_date}.")
            else:
                logger.warning(f"No historical data for Naked Put ID {screener_id} ({symbol}) from {start_date} to {end_date}. Skipping.")
    
    # Backtest Covered Calls
    logger.info("Starting backtesting for Covered Calls.")

    # Loop over chunks of the DataFrame for Covered Calls
    for batch in chunk_dataframe(covered_call_data, batch_size):
        # Get the list of screener_ids from the batch
        screener_ids = batch['id'].tolist()
        
        # Fetch the last backtest records in batch
        last_backtest_results = get_last_backtest_batch(supabase_client, "covered_call_backtest_results", screener_ids)
        
        for index, record in batch.iterrows():
            symbol = record["symbol"]

            # Validate symbol
            if not is_valid_symbol(symbol, supabase_client.polygon_client):
                logger.warning(f"Invalid or unsupported symbol: {symbol}. Skipping backtest for ID {record['id']}.")
                continue

            upload_date = datetime.strptime(record["csv_download_date"], "%Y-%m-%d").date()
            expiration = datetime.strptime(record["expiration_date"], "%Y-%m-%d").date()
            today = date.today()
            expiration_in_future = expiration > today

            # Retrieve the last backtest result for the screener_id in the batch
            screener_id = record['id']
            last_backtest = last_backtest_results.get(screener_id)

            if last_backtest:
                last_date, capital, cumulative_profit, status = last_backtest
                start_date = last_date + timedelta(days=1)
                logger.info(f"Last backtest for Covered Call ID {screener_id}: Date={last_date}, Capital={capital}, Cumulative Profit={cumulative_profit}, Status={status}")
            else:
                start_date = upload_date
                capital = 5000.00  # STARTING_CAPITAL
                cumulative_profit = 0.0
                status = "Active"
                logger.info(f"No previous backtest found for Covered Call ID {screener_id}. Starting from upload date {upload_date}.")

            # Determine the end date
            end_date = min(expiration, today)

            # Check types before comparison
            if not isinstance(start_date, date) or not isinstance(end_date, date):
                logger.error(f"Invalid date types: start_date={start_date}, end_date={end_date}")
                continue

            if start_date > end_date:
                logger.info(f"No new data to backtest for Covered Call ID {screener_id} ({symbol}).")
                continue

            logger.info(f"Backtesting Covered Call ID {screener_id} for {symbol}: Start Date = {start_date}, End Date = {end_date}, Expiration in Future: {expiration_in_future}")

            hist_df = get_historical_prices(symbol, start_date, end_date)
            if hist_df is not None and not hist_df.empty:
                backtest_covered_call(
                    supabase_client,
                    record,
                    hist_df,
                    start_date,
                    capital,
                    cumulative_profit,
                    status,
                )
                logger.info(f"Backtested Covered Call ID {screener_id} ({symbol}) from {start_date} to {end_date}.")
            else:
                logger.warning(f"No historical data for Covered Call ID {screener_id} ({symbol}) from {start_date} to {end_date}. Skipping.")
    logger.info("Backtesting process completed.")



def populate_advanced_screener(supabase_client):
    """
    Populate the advanced_screener table with PoP, IV, Sector, Industry, Story, ML Prediction, and calculate ml_ranking.
    Handles duplicate entries by checking for existing records before inserting.
    """
    try:
        # Fetch all non-expired screener_ids from both naked_put_screener_current and covered_call_screener_current
        naked_put_records = (
            supabase_client.table("naked_put_screener_current")
            .select("id", "symbol", "iv")
            .execute()
            .data
        )
        covered_call_records = (
            supabase_client.table("covered_call_screener_current")
            .select("id", "symbol", "iv")
            .execute()
            .data
        )
        # Fetch Sector, Industry, Story data
        logger.info("Fetching Sector, Industry, and Story data for all symbols.")
        # industry_data = get_industry_data(symbols)
        # industry_df = pd.DataFrame(industry_data).set_index("Ticker")

        # Prepare lists for inserts and updates
        advanced_screener_entries = []

        # Process Naked Puts
        logger.info("Processing Naked Put records for Advanced Screener.")
        advanced_screener_entries = calculate_PoP(
            supabase_client, "naked_put_backtest_results", naked_put_records, advanced_screener_entries, "Naked Put"
        )

        # Process Covered Calls
        logger.info("Processing Covered Call records for Advanced Screener.")
        advanced_screener_entries = calculate_PoP(
            supabase_client, "covered_call_backtest_results", covered_call_records, advanced_screener_entries, "Covered Call"
        )

        # Upsert the record into the 'advanced_screener' table
        response = supabase_client.table("advanced_screener").upsert(advanced_screener_entries, on_conflict="screener_id, strategy_type").execute()

        # Check if the operation was successful
        if response:
            print("Upsert operation completed successfully.")
        else:
            print(f"Error during upsert: {response}")
    except Exception as e:
        logger.error(f"Error populating advanced_screener table: {e}")


# def fetch_industry_data(industry_df, symbol):
#     """Fetch Sector, Industry, and Story for the given symbol."""
#     if symbol in industry_df.index:
#         sector = industry_df.at[symbol, "sector"]
#         industry = industry_df.at[symbol, "industry"]
#         story = industry_df.at[symbol, "story"]
#     else:
#         sector = "N/A"
#         industry = "N/A"
#         story = "N/A"
#     return sector, industry, story


# def update_advanced_screener(supabase_client):
#     """
#     Update existing entries in advanced_screener table with latest PoP, IV, Sector, Industry, Story, ML Prediction, and ml_ranking.
#     """
#     try:
#         # Fetch all entries from advanced_screener
#         response = supabase_client.table("advanced_screener").select("*").execute()
#         advanced_screener_data = pd.DataFrame(response.data)

#         if advanced_screener_data.empty:
#             logger.info("No entries found in advanced_screener to update.")
#             return

#         # Get unique symbols to fetch industry data
#         symbols = list(
#             {row["symbol"] for index, row in advanced_screener_data.iterrows()}
#         )
#         logger.info(
#             "Fetching updated Sector, Industry, and Story data for all symbols."
#         )
#         # industry_data = get_industry_data(symbols)
#         # industry_df = pd.DataFrame(industry_data).set_index("Ticker")

#         for index, row in advanced_screener_data.iterrows():
#             screener_id = row["screener_id"]
#             symbol = row["symbol"]
#             strategy_type = row["strategy_type"]
#             print(strategy_type)

#             if strategy_type == "Naked Put":
#                 table_name = "naked_put_backtest_results"
#                 screener_table = "naked_put_screener_current"
#             elif strategy_type == "Covered Call":
#                 table_name = "covered_call_backtest_results"
#                 screener_table = "covered_call_screener_current"
#             else:
#                 logger.warning(
#                     f"Unknown strategy_type {strategy_type} for screener_id {screener_id}. Setting prediction None."
#                 )
#                 continue

#             # Calculate PoP
#             pop = calculate_PoP(supabase_client, table_name, screener_id)

#             # Fetch IV
#             iv = calculate_average_IV(supabase_client, screener_table, screener_id)

#             # Prepare features and prediction
#             features = get_latest_features(symbol, get_historical_prices)
#             if features:
#                 prediction, prediction_prob = make_prediction(symbol, features)
#             else:
#                 prediction = None

#             # Calculate ml_ranking
#             ml_ranking = calculate_ml_ranking(
#                 pop, iv, prediction if prediction is not None else 0
#             )

#             # Fetch Sector, Industry, Story
#             # if symbol in industry_df.index:
#             #     sector = industry_df.at[symbol, "sector"]
#             #     industry = industry_df.at[symbol, "industry"]
#             #     story = industry_df.at[symbol, "story"]
#             # else:
#             # sector = "N/A"
#             # industry = "N/A"
#             # story = "N/A"

#             # Update the advanced_screener entry
#             update_data = {
#                 "PoP": pop,
#                 "IV": iv,
#                 # "sector": sector,
#                 # "industry": industry,
#                 # "story": story,
#                 "ml_prediction": prediction_prob if prediction_prob is not None else "N/A",
#                 "ml_ranking": ml_ranking,
#                 "ml_opinion": categorize_prob(prediction_prob),
#                 "created_at": datetime.utcnow().isoformat(),
#             }
#             response = (
#                 supabase_client.table("advanced_screener")
#                 .update(update_data)
#                 .eq("screener_id", screener_id)
#                 .execute()
#             )

#             if hasattr(response, "error") and response.error:
#                 logger.error(
#                     f"Failed to update Advanced Screener for screener_id {screener_id}: {response.error}"
#                 )
#             else:
#                 logger.info(
#                     f"Updated Advanced Screener for screener_id {screener_id} with PoP {pop}%, IV {iv}"  #, Sector {sector}, Industry {industry}."
#                 )

#     except Exception as e:
#         logger.error(f"Error updating advanced_screener table: {e}")


def update_screener_with_backtest(supabase_client):
    """
    Update screener tables with aggregated backtest metrics and populate advanced_screener.
    """
    logger.info("Starting to update screener tables with backtest metrics.")
    try:
        # Update Naked Put Screener
        naked_put_results = (
            supabase_client.table("naked_put_backtest_results_current").select("*").execute()
        )
        naked_put_df = pd.DataFrame(naked_put_results.data)

        if not naked_put_df.empty:
            agg_naked_put = (
                naked_put_df.groupby("screener_id")
                .agg(
                    average_profit=("daily_profit", "mean"),
                    total_profit=("cumulative_profit", "max"),
                    win_rate=("daily_profit", lambda x: (x > 0).mean()),
                )
                .reset_index()
            )

            # Prepare batch data for update
            update_data_naked_put = [
                {
                    "id": int(row["screener_id"]),
                    "average_profit": round(row["average_profit"], 2),
                    "total_profit": round(row["total_profit"], 2),
                    "win_rate": round(row["win_rate"], 2),
                }
                for _, row in agg_naked_put.iterrows()
            ]

            # Update in batch
            response = (
                supabase_client.table("naked_put_screener")
                .upsert(update_data_naked_put)
                .execute()
            )
            if hasattr(response, "error") and response.error:
                logger.error(f"Failed to update Naked Put Screener: {response.error}")
            elif hasattr(response, "data") and response.data:
                logger.info(f"Updated Naked Put Screener with metrics.")
            else:
                logger.warning(f"Update Naked Put Screener returned an unexpected response: {response}")

        # Update Covered Call Screener
        covered_call_results = (
            supabase_client.table("covered_call_backtest_results_current").select("*").execute()
        )
        covered_call_df = pd.DataFrame(covered_call_results.data)

        if not covered_call_df.empty:
            agg_covered_call = (
                covered_call_df.groupby("screener_id")
                .agg(
                    average_profit=("daily_profit", "mean"),
                    total_profit=("cumulative_profit", "max"),
                    win_rate=("daily_profit", lambda x: (x > 0).mean()),
                )
                .reset_index()
            )

            # Prepare batch data for update
            update_data_covered_call = [
                {
                    "id": int(row["screener_id"]),
                    "average_profit": round(row["average_profit"], 2),
                    "total_profit": round(row["total_profit"], 2),
                    "win_rate": round(row["win_rate"], 2),
                }
                for _, row in agg_covered_call.iterrows()
            ]

            # Update in batch
            response = (
                supabase_client.table("covered_call_screener")
                .upsert(update_data_covered_call)
                .execute()
            )
            if hasattr(response, "error") and response.error:
                logger.error(f"Failed to update Covered Call Screener: {response.error}")
            elif hasattr(response, "data") and response.data:
                logger.info(f"Updated Covered Call Screener with metrics.")
            else:
                logger.warning(f"Update Covered Call Screener returned an unexpected response: {response}")

        logger.info("Screener tables updated with backtest metrics.")
    except Exception as e:
        logger.error(f"Error updating screener tables: {e}")
