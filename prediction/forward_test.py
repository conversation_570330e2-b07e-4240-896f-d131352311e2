import pandas as pd
from backtest.position import Position
from ml.features import extract_features

def run_forward_test(position: Position, historical_data: pd.DataFrame, ml_model):
    """
    Separate forward testing using ML predictions
    """
    features = extract_features(historical_data)
    prediction = ml_model.predict(features)
    
    return {
        'prediction': prediction,
        'confidence': ml_model.predict_proba(features)[0],
        'features_used': features.columns.tolist()
    } 